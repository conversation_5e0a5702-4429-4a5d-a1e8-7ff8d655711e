#!/bin/bash

# =============================================================================
# Flutter Web Build and Deployment Script for Almashal Family App
# =============================================================================
# This script automates the process of building the Flutter web app and 
# deploying it to the Laravel public directory.
#
# Usage: ./deploy-web.sh
# Make executable: chmod +x deploy-web.sh
# =============================================================================

# Script configuration
SCRIPT_NAME="Almashal Family Web Deployment"
FLUTTER_PROJECT_DIR="$(pwd)"
BUILD_DIR="$FLUTTER_PROJECT_DIR/build/web"
TARGET_DIR="/Users/<USER>/Desktop/laravel projects/Maalem/almashal_family/public/app"
BASE_HREF="/app/"

# Colors for console output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# =============================================================================
# Utility Functions
# =============================================================================

# Print colored output with timestamp
print_step() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} ${CYAN}$1${NC}"
}

print_success() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} ${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} ${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} ${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} ${PURPLE}ℹ️  $1${NC}"
}

# Print script header
print_header() {
    echo -e "${CYAN}=============================================================================${NC}"
    echo -e "${CYAN}                    $SCRIPT_NAME${NC}"
    echo -e "${CYAN}=============================================================================${NC}"
    echo ""
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# =============================================================================
# Pre-flight Checks
# =============================================================================

check_prerequisites() {
    print_step "Performing pre-flight checks..."
    
    # Check if Flutter is installed
    if ! command_exists flutter; then
        print_error "Flutter is not installed or not in PATH"
        exit 1
    fi
    
    # Check if we're in a Flutter project directory
    if [ ! -f "pubspec.yaml" ]; then
        print_error "Not in a Flutter project directory (pubspec.yaml not found)"
        exit 1
    fi
    
    # Check Flutter version and web support
    print_info "Flutter version: $(flutter --version | head -n 1)"
    
    # Verify web platform is enabled
    if ! flutter devices | grep -q "Chrome"; then
        print_warning "Chrome not detected, but continuing with build..."
    fi
    
    print_success "Pre-flight checks completed"
}

# =============================================================================
# Build Process
# =============================================================================

build_flutter_web() {
    print_step "Starting Flutter web build process..."
    print_info "Build configuration:"
    print_info "  - Mode: Release"
    print_info "  - Base HREF: $BASE_HREF"
    print_info "  - Output: $BUILD_DIR"

    # Clean previous build
    print_step "Cleaning previous build artifacts..."
    flutter clean

    # Get dependencies
    print_step "Getting Flutter dependencies..."
    if ! flutter pub get; then
        print_error "Failed to get Flutter dependencies"
        exit 1
    fi

    # Copy logo assets to web directory before build
    print_step "Copying logo assets to web directory..."
    cp assets/images/logo.png web/logo.png 2>/dev/null || true
    cp assets/vectors/logo.svg web/logo.svg 2>/dev/null || true
    cp assets/vectors/almaalem-logo.svg web/almaalem-logo.svg 2>/dev/null || true

    # Build for web
    print_step "Building Flutter web application..."
    if ! flutter build web --release --base-href="$BASE_HREF"; then
        print_error "Flutter web build failed"
        exit 1
    fi

    # Verify build output exists
    if [ ! -d "$BUILD_DIR" ]; then
        print_error "Build directory not found: $BUILD_DIR"
        exit 1
    fi

    # Check if essential files exist
    if [ ! -f "$BUILD_DIR/index.html" ]; then
        print_error "Build appears incomplete - index.html not found"
        exit 1
    fi

    print_success "Flutter web build completed successfully"
    print_info "Build size: $(du -sh "$BUILD_DIR" | cut -f1)"
}

# =============================================================================
# Deployment Process
# =============================================================================

prepare_target_directory() {
    print_step "Preparing target directory..."
    
    # Create target directory if it doesn't exist
    if [ ! -d "$TARGET_DIR" ]; then
        print_info "Creating target directory: $TARGET_DIR"
        if ! mkdir -p "$TARGET_DIR"; then
            print_error "Failed to create target directory"
            exit 1
        fi
    fi
    
    # Clean existing files in target directory
    if [ "$(ls -A "$TARGET_DIR" 2>/dev/null)" ]; then
        print_step "Cleaning existing files in target directory..."
        if ! rm -rf "$TARGET_DIR"/*; then
            print_error "Failed to clean target directory"
            exit 1
        fi
        print_success "Target directory cleaned"
    else
        print_info "Target directory is already empty"
    fi
}

deploy_files() {
    print_step "Deploying files to target directory..."
    print_info "Source: $BUILD_DIR"
    print_info "Target: $TARGET_DIR"
    
    # Copy all files from build directory to target directory
    if ! cp -R "$BUILD_DIR"/* "$TARGET_DIR"/; then
        print_error "Failed to copy files to target directory"
        exit 1
    fi
    
    # Verify deployment
    if [ ! -f "$TARGET_DIR/index.html" ]; then
        print_error "Deployment verification failed - index.html not found in target"
        exit 1
    fi
    
    print_success "Files deployed successfully"
    print_info "Deployed size: $(du -sh "$TARGET_DIR" | cut -f1)"
}

# =============================================================================
# Post-deployment Tasks
# =============================================================================

verify_deployment() {
    print_step "Verifying deployment..."
    
    # Check essential files
    local essential_files=("index.html" "main.dart.js" "flutter.js" "manifest.json")
    
    for file in "${essential_files[@]}"; do
        if [ -f "$TARGET_DIR/$file" ]; then
            print_success "✓ $file"
        else
            print_warning "✗ $file (missing)"
        fi
    done
    
    # Check assets directory
    if [ -d "$TARGET_DIR/assets" ]; then
        print_success "✓ assets directory"
    else
        print_warning "✗ assets directory (missing)"
    fi
    
    print_success "Deployment verification completed"
}

print_deployment_summary() {
    echo ""
    print_step "Deployment Summary"
    echo -e "${CYAN}=============================================================================${NC}"
    print_info "Project: Almashal Family Flutter Web App"
    print_info "Build Mode: Release"
    print_info "Base HREF: $BASE_HREF"
    print_info "Source: $BUILD_DIR"
    print_info "Target: $TARGET_DIR"
    print_info "Deployment Time: $(date)"
    echo -e "${CYAN}=============================================================================${NC}"
    print_success "🚀 Deployment completed successfully!"
    echo ""
    print_info "Next steps:"
    print_info "1. Test the web application in your browser"
    print_info "2. Verify all features work correctly"
    print_info "3. Check browser console for any errors"
    echo ""
}

# =============================================================================
# Error Handling
# =============================================================================

# Trap errors and cleanup
cleanup_on_error() {
    print_error "Script interrupted or failed"
    print_info "Cleaning up..."
    # Add any cleanup tasks here if needed
    exit 1
}

# Set up error handling
trap cleanup_on_error ERR INT TERM

# =============================================================================
# Main Execution
# =============================================================================

main() {
    # Print header
    print_header
    
    # Start deployment process
    print_step "Starting deployment process..."
    
    # Execute deployment steps
    check_prerequisites
    build_flutter_web
    prepare_target_directory
    deploy_files
    verify_deployment
    print_deployment_summary
    
    # Exit successfully
    exit 0
}

# Execute main function
main "$@"

import 'package:flutter_test/flutter_test.dart';
import 'package:almashal/src/data/models/family_tree_connection_request.dart';
import 'package:almashal/src/data/models/family_tree_node.dart';
import 'package:almashal/src/data/models/responses/family_tree_nodes_response.dart';
import 'package:almashal/src/data/models/responses/connection_requests_response.dart';

void main() {
  group('Family Tree Connection Models', () {
    test('FamilyTreeConnectionRequest model should serialize correctly', () {
      final request = FamilyTreeConnectionRequest(
        id: 1,
        familyMemberId: 123,
        familyTreeNodeId: 456,
        status: FamilyTreeConnectionRequest.statusPending,
        note: 'Test connection request',
        requestedAt: DateTime.now(),
      );

      expect(request.id, 1);
      expect(request.familyMemberId, 123);
      expect(request.familyTreeNodeId, 456);
      expect(request.status, FamilyTreeConnectionRequest.statusPending);
      expect(request.note, 'Test connection request');
      expect(request.isPending, true);
      expect(request.isApproved, false);
      expect(request.isRejected, false);
    });

    test('FamilyTreeConnectionRequest status display text should be correct', () {
      final pendingRequest = FamilyTreeConnectionRequest(
        id: 1,
        familyMemberId: 123,
        familyTreeNodeId: 456,
        status: FamilyTreeConnectionRequest.statusPending,
        requestedAt: DateTime.now(),
      );

      final approvedRequest = FamilyTreeConnectionRequest(
        id: 2,
        familyMemberId: 123,
        familyTreeNodeId: 456,
        status: FamilyTreeConnectionRequest.statusApproved,
        requestedAt: DateTime.now(),
      );

      final rejectedRequest = FamilyTreeConnectionRequest(
        id: 3,
        familyMemberId: 123,
        familyTreeNodeId: 456,
        status: FamilyTreeConnectionRequest.statusRejected,
        requestedAt: DateTime.now(),
      );

      expect(pendingRequest.statusDisplayText, 'قيد المراجعة');
      expect(approvedRequest.statusDisplayText, 'مقبول');
      expect(rejectedRequest.statusDisplayText, 'مرفوض');
    });

    test('FamilyTreeNodesResponse should handle pagination correctly', () {
      final nodes = [
        FamilyTreeNode(
          id: 1,
          name: 'Test Node 1',
          nickName: null,
          overview: null,
          image: null,
          thumbImageUrl: null,
          order: null,
          gender: null,
          alive: null,
          birthDate: null,
          deathDate: null,
          birthPlace: null,
          deathPlace: null,
          job: null,
          address: null,
          children: [],
          parent: null,
        ),
        FamilyTreeNode(
          id: 2,
          name: 'Test Node 2',
          nickName: null,
          overview: null,
          image: null,
          thumbImageUrl: null,
          order: null,
          gender: null,
          alive: null,
          birthDate: null,
          deathDate: null,
          birthPlace: null,
          deathPlace: null,
          job: null,
          address: null,
          children: [],
          parent: null,
        ),
      ];

      final response = FamilyTreeNodesResponse(
        total: 20,
        currentPage: 1,
        lastPage: 2,
        perPage: 15,
        nodes: nodes,
      );

      expect(response.total, 20);
      expect(response.currentPage, 1);
      expect(response.lastPage, 2);
      expect(response.perPage, 15);
      expect(response.nodes.length, 2);
      expect(response.hasNextPage, true);
      expect(response.hasPreviousPage, false);
    });

    test('ConnectionRequestsResponse should handle empty list correctly', () {
      final response = ConnectionRequestsResponse(
        total: 0,
        currentPage: 1,
        lastPage: 1,
        perPage: 15,
        requests: [],
      );

      expect(response.total, 0);
      expect(response.currentPage, 1);
      expect(response.lastPage, 1);
      expect(response.perPage, 15);
      expect(response.requests.isEmpty, true);
      expect(response.hasNextPage, false);
      expect(response.hasPreviousPage, false);
    });
  });

  group('Family Tree Connection Request JSON Serialization', () {
    test('should serialize and deserialize correctly', () {
      final originalRequest = FamilyTreeConnectionRequest(
        id: 1,
        familyMemberId: 123,
        familyTreeNodeId: 456,
        status: FamilyTreeConnectionRequest.statusPending,
        note: 'Test note',
        requestedAt: DateTime.parse('2024-01-01T10:00:00Z'),
        reviewedAt: DateTime.parse('2024-01-02T10:00:00Z'),
        reviewedBy: 789,
      );

      final json = originalRequest.toJson();
      final deserializedRequest = FamilyTreeConnectionRequest.fromJson(json);

      expect(deserializedRequest.id, originalRequest.id);
      expect(deserializedRequest.familyMemberId, originalRequest.familyMemberId);
      expect(deserializedRequest.familyTreeNodeId, originalRequest.familyTreeNodeId);
      expect(deserializedRequest.status, originalRequest.status);
      expect(deserializedRequest.note, originalRequest.note);
      expect(deserializedRequest.requestedAt, originalRequest.requestedAt);
      expect(deserializedRequest.reviewedAt, originalRequest.reviewedAt);
      expect(deserializedRequest.reviewedBy, originalRequest.reviewedBy);
    });
  });
}

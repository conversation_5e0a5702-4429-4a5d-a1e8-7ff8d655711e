import 'package:flutter_test/flutter_test.dart';
import 'package:almashal/src/data/models/profile/review.dart';
import 'package:almashal/src/data/models/comment_user.dart';
import 'package:almashal/src/data/models/responses/review_summary_response.dart';
import 'package:almashal/src/data/models/responses/add_review_response.dart';
import 'package:almashal/src/data/models/responses/reviews_list_response.dart';
import 'package:almashal/src/data/models/meta.dart';

void main() {
  group('Reviews System Tests', () {
    test('Review model should parse JSON correctly', () {
      final json = {
        'id': 1,
        'rating': 5,
        'comment': 'Great family member!',
        'reviewer': {
          'id': 1,
          'display_name': '<PERSON>',
          'image': 'http://example.com/image.jpg',
        },
        'reviewed_user': {
          'id': 2,
          'display_name': '<PERSON>',
          'image': 'http://example.com/image2.jpg',
        },
        'status': true,
        'created_at': '2025-04-18 12:00:00',
        'created_at_formatted': '18 April 2025 12:00 PM',
        'updated_at': '2025-04-18 12:00:00',
      };

      final review = Review.fromJson(json);

      expect(review.id, 1);
      expect(review.rating, 5);
      expect(review.comment, 'Great family member!');
      expect(review.reviewer.id, 1);
      expect(review.reviewer.displayName, 'John Doe');
      expect(review.reviewedUser.id, 2);
      expect(review.status, true);
      expect(review.createdAtFormatted, '18 April 2025 12:00 PM');
    });

    test('Review model should handle missing optional fields', () {
      final json = {
        'id': 1,
        'rating': 3,
        'reviewer': {
          'id': 1,
          'display_name': 'John Doe',
          'image': 'http://example.com/image.jpg',
        },
        'reviewed_user': {
          'id': 2,
          'display_name': 'Jane Doe',
          'image': 'http://example.com/image2.jpg',
        },
      };

      final review = Review.fromJson(json);

      expect(review.id, 1);
      expect(review.rating, 3);
      expect(review.comment, ''); // Should default to empty string
      expect(review.status, true); // Should default to true
      expect(review.createdAt, ''); // Should default to empty string
      expect(review.createdAtFormatted, ''); // Should default to empty string
    });

    test('Review should correctly identify reviewer permissions', () {
      final json = {
        'id': 1,
        'rating': 5,
        'comment': 'Test',
        'reviewer': {
          'id': 123,
          'display_name': 'John Doe',
          'image': 'http://example.com/image.jpg',
        },
        'reviewed_user': {
          'id': 2,
          'display_name': 'Jane Doe',
          'image': 'http://example.com/image2.jpg',
        },
        'status': true,
        'created_at': '2025-04-18 12:00:00',
        'created_at_formatted': '18 April 2025 12:00 PM',
        'updated_at': '2025-04-18 12:00:00',
      };

      final review = Review.fromJson(json);

      // Should return true for the reviewer
      expect(review.isReviewerCurrentUser(123), true);

      // Should return false for other users
      expect(review.isReviewerCurrentUser(456), false);
      expect(review.isReviewerCurrentUser(2), false);
    });

    test('ReviewSummaryResponse should parse JSON correctly', () {
      final json = {
        'data': {
          'user': {
            'id': 1,
            'display_name': 'Jane Doe',
            'image': 'http://example.com/image.jpg',
          },
          'reviews_count': 5,
          'average_rating': 4.2,
          'ratings_breakdown': {
            '5': 2,
            '4': 2,
            '3': 1,
            '2': 0,
            '1': 0,
          },
        },
        'message': 'Success',
      };

      final response = ReviewSummaryResponse.fromJson(json);

      expect(response.data.user.id, 1);
      expect(response.data.reviewsCount, 5);
      expect(response.data.averageRating, 4.2);
      expect(response.data.ratingsBreakdown['5'], 2);
      expect(response.data.ratingsBreakdown['4'], 2);
      expect(response.data.ratingsBreakdown['3'], 1);
      expect(response.message, 'Success');
    });

    test('AddReviewResponse should parse JSON correctly', () {
      final json = {
        'message': 'Review created successfully',
        'data': {
          'id': 1,
          'rating': 5,
          'comment': 'Great!',
          'reviewer': {
            'id': 1,
            'display_name': 'John Doe',
            'image': 'http://example.com/image.jpg',
          },
          'reviewed_user': {
            'id': 2,
            'display_name': 'Jane Doe',
            'image': 'http://example.com/image2.jpg',
          },
          'status': true,
          'created_at': '2025-04-18 12:00:00',
          'created_at_formatted': '18 April 2025 12:00 PM',
          'updated_at': '2025-04-18 12:00:00',
        },
      };

      final response = AddReviewResponse.fromJson(json);

      expect(response.message, 'Review created successfully');
      expect(response.data, isNotNull);
      expect(response.data!.id, 1);
      expect(response.data!.rating, 5);
      expect(response.data!.comment, 'Great!');
    });

    test('ReviewsListResponse should parse JSON correctly', () {
      final json = {
        'data': [
          {
            'id': 1,
            'rating': 5,
            'comment': 'Great!',
            'reviewer': {
              'id': 1,
              'display_name': 'John Doe',
              'image': 'http://example.com/image.jpg',
            },
            'reviewed_user': {
              'id': 2,
              'display_name': 'Jane Doe',
              'image': 'http://example.com/image2.jpg',
            },
            'status': true,
            'created_at': '2025-04-18 12:00:00',
            'created_at_formatted': '18 April 2025 12:00 PM',
            'updated_at': '2025-04-18 12:00:00',
          }
        ],
        'meta': {
          'current_page': 1,
          'last_page': 1,
          'per_page': 10,
          'total': 1,
        },
        'message': 'Success',
      };

      final response = ReviewsListResponse.fromJson(json);

      expect(response.data.length, 1);
      expect(response.data.first.id, 1);
      expect(response.meta.currentPage, 1);
      expect(response.meta.lastPage, 1);
      expect(response.meta.total, 1);
    });
  });
}

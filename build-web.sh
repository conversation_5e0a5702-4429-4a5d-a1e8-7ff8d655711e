#!/bin/bash

# =============================================================================
# Flutter Web Build Script for Almashal Family App (Build Only)
# =============================================================================
# This script only builds the Flutter web app without deploying it.
# Useful for testing builds or when you want to manually deploy later.
#
# Usage: ./build-web.sh
# Make executable: chmod +x build-web.sh
# =============================================================================

# Script configuration
SCRIPT_NAME="Almashal Family Web Build"
FLUTTER_PROJECT_DIR="$(pwd)"
BUILD_DIR="$FLUTTER_PROJECT_DIR/build/web"
BASE_HREF="/app/"

# Colors for console output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# =============================================================================
# Utility Functions
# =============================================================================

print_step() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} ${CYAN}$1${NC}"
}

print_success() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} ${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} ${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} ${PURPLE}ℹ️  $1${NC}"
}

print_header() {
    echo -e "${CYAN}=============================================================================${NC}"
    echo -e "${CYAN}                    $SCRIPT_NAME${NC}"
    echo -e "${CYAN}=============================================================================${NC}"
    echo ""
}

command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# =============================================================================
# Build Process
# =============================================================================

check_prerequisites() {
    print_step "Performing pre-flight checks..."
    
    if ! command_exists flutter; then
        print_error "Flutter is not installed or not in PATH"
        exit 1
    fi
    
    if [ ! -f "pubspec.yaml" ]; then
        print_error "Not in a Flutter project directory (pubspec.yaml not found)"
        exit 1
    fi
    
    print_info "Flutter version: $(flutter --version | head -n 1)"
    print_success "Pre-flight checks completed"
}

build_flutter_web() {
    print_step "Starting Flutter web build process..."
    print_info "Build configuration:"
    print_info "  - Mode: Release"
    print_info "  - Base HREF: $BASE_HREF"
    print_info "  - Output: $BUILD_DIR"

    # Clean previous build
    print_step "Cleaning previous build artifacts..."
    flutter clean

    # Get dependencies
    print_step "Getting Flutter dependencies..."
    if ! flutter pub get; then
        print_error "Failed to get Flutter dependencies"
        exit 1
    fi

    # Copy logo assets to web directory before build
    print_step "Copying logo assets to web directory..."
    cp assets/images/logo.png web/logo.png 2>/dev/null || true
    cp assets/vectors/logo.svg web/logo.svg 2>/dev/null || true
    cp assets/vectors/almaalem-logo.svg web/almaalem-logo.svg 2>/dev/null || true

    # Build for web
    print_step "Building Flutter web application..."
    if ! flutter build web --release --base-href="$BASE_HREF"; then
        print_error "Flutter web build failed"
        exit 1
    fi

    # Verify build output
    if [ ! -d "$BUILD_DIR" ] || [ ! -f "$BUILD_DIR/index.html" ]; then
        print_error "Build appears incomplete"
        exit 1
    fi

    print_success "Flutter web build completed successfully"
    print_info "Build size: $(du -sh "$BUILD_DIR" | cut -f1)"
    print_info "Build location: $BUILD_DIR"
}

print_build_summary() {
    echo ""
    print_step "Build Summary"
    echo -e "${CYAN}=============================================================================${NC}"
    print_info "Project: Almashal Family Flutter Web App"
    print_info "Build Mode: Release"
    print_info "Base HREF: $BASE_HREF"
    print_info "Output: $BUILD_DIR"
    print_info "Build Time: $(date)"
    echo -e "${CYAN}=============================================================================${NC}"
    print_success "🎉 Build completed successfully!"
    echo ""
    print_info "To deploy, run: ./deploy-web.sh"
    print_info "To test locally, run: cd build/web && python -m http.server 8080"
    echo ""
}

# =============================================================================
# Main Execution
# =============================================================================

main() {
    print_header
    print_step "Starting build process..."
    
    check_prerequisites
    build_flutter_web
    print_build_summary
    
    exit 0
}

# Execute main function
main "$@"

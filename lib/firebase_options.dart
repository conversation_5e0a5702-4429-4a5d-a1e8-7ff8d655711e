// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      // إعداد مؤقت للويب حتى يتم إعادة تكوينه بواسطة flutterfire
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  // إعدادات Firebase للويب
  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAUipGS4_co9b1Mmi0Rh_ZNqJDOPYa5mGA',
    appId: '1:531104149238:web:c77af25c0eff825b14c6d7',
    messagingSenderId: '531104149238',
    projectId: 'almashalfamily-35d1a',
    authDomain: 'almashalfamily-35d1a.firebaseapp.com',
    storageBucket: 'almashalfamily-35d1a.appspot.com',
    measurementId: 'G-MEASUREMENT_ID',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAUipGS4_co9b1Mmi0Rh_ZNqJDOPYa5mGA',
    appId: '1:531104149238:android:c77af25c0eff825b14c6d7',
    messagingSenderId: '531104149238',
    projectId: 'almashalfamily-35d1a',
    storageBucket: 'almashalfamily-35d1a.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyC6fAGIALDqzvRy0kVQuNnO8TD6BTJmr9U',
    appId: '1:531104149238:ios:6fc54c6739d7cb4014c6d7',
    messagingSenderId: '531104149238',
    projectId: 'almashalfamily-35d1a',
    storageBucket: 'almashalfamily-35d1a.appspot.com',
    iosBundleId: 'com.almashalfamily.app',
  );
}

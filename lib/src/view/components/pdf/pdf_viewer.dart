import 'package:flutter_cached_pdfview/flutter_cached_pdfview.dart';
import 'package:almashal/src/core/utils/platform_helper.dart';
import 'package:flutter/material.dart';
// Conditional imports for web-specific functionality
import 'pdf_viewer_web.dart' if (dart.library.io) 'pdf_viewer_stub.dart';

class PdfViewer extends StatelessWidget {
  const PdfViewer({super.key, required this.file});

  final String file;

  @override
  Widget build(BuildContext context) {
    if (PlatformHelper.isWeb) {
      // For web, use the web-specific PDF viewer
      return WebPdfViewer(file: file);
    } else {
      // For mobile, use flutter_cached_pdfview
      return const PDF(
        pageSnap: false,
        pageFling: false,
        swipeHorizontal: false,
      ).cachedFromUrl(file);
    }
  }
}

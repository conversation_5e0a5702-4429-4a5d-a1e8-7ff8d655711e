import 'package:flutter/material.dart';

// Stub implementation for mobile platforms
class WebPdfViewer extends StatelessWidget {
  const WebPdfViewer({super.key, required this.file});

  final String file;

  @override
  Widget build(BuildContext context) {
    // This should never be called on mobile platforms
    // as we use flutter_cached_pdfview instead
    return const Center(
      child: Text(
        'PDF viewer not available on this platform',
        style: TextStyle(fontSize: 16),
      ),
    );
  }
}

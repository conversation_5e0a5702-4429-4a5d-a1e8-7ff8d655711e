import 'package:almashal/src/core/routes/app_pages.dart';
import 'package:almashal/src/core/utils/platform_helper.dart';
import 'package:feather_icons/feather_icons.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// زر عائم للعودة إلى الصفحة الرئيسية - يظهر فقط في منصة الويب
/// ويظهر في جميع الصفحات عدا الصفحة الرئيسية
class WebHomeFloatingActionButton extends StatelessWidget {
  const WebHomeFloatingActionButton({super.key});

  /// تحقق من ما إذا كان يجب إظهار الزر في المسار الحالي
  bool _shouldShowButton() {
    final currentRoute = Get.currentRoute;

    // قائمة الصفحات التي لا نريد إظهار الزر فيها
    final hiddenRoutes = [
      Routes.HOME_PAGE,
      Routes.SPLASH_PAGE,
      Routes.LOGIN_PAGE,
      Routes.REGISTER_PAGE,
      Routes.REGISTRATION_PENDING_PAGE,
      Routes.FORGET_PASSWORD_PAGE,
      Routes.RESET_PASSWORD_PAGE,
    ];

    return !hiddenRoutes.contains(currentRoute);
  }

  @override
  Widget build(BuildContext context) {
    // إظهار الزر فقط في منصة الويب
    if (!PlatformHelper.isWeb) {
      return const SizedBox.shrink();
    }

    // إخفاء الزر في الصفحات المحددة
    if (!_shouldShowButton()) {
      return const SizedBox.shrink();
    }

    return Positioned(
      bottom: 20,
      left: 20, // في التطبيق العربي، نضع الزر على اليسار
      child: FloatingActionButton.small(
        onPressed: () {
          Get.offAllNamed(Routes.HOME_PAGE);
        },
        tooltip: 'العودة إلى الرئيسية',
        heroTag: 'web_home_fab', // تجنب تضارب الـ hero tags
        child: const Icon(
          FeatherIcons.home,
          size: 20,
        ),
      ),
    );
  }
}

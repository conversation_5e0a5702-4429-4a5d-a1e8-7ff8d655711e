import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../../data/models/family_tree_node.dart';
import '../../../core/values/app_styles.dart';

class FamilyTreeNodeCard extends StatelessWidget {
  final FamilyTreeNode node;
  final VoidCallback? onTap;
  final bool isSelected;
  final bool showSelectionIndicator;

  const FamilyTreeNodeCard({
    super.key,
    required this.node,
    this.onTap,
    this.isSelected = false,
    this.showSelectionIndicator = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: isSelected ? 8 : 2,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      shape: RoundedRectangleBorder(
        borderRadius: AppStyles.cardBorderRadius,
        side: isSelected 
            ? BorderSide(color: Theme.of(context).primaryColor, width: 2)
            : BorderSide.none,
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: AppStyles.cardBorderRadius,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Profile Image
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(30),
                  border: Border.all(
                    color: Theme.of(context).dividerColor,
                    width: 1,
                  ),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(30),
                  child: node.image != null && node.image!.isNotEmpty
                      ? CachedNetworkImage(
                          imageUrl: node.image!,
                          fit: BoxFit.cover,
                          placeholder: (context, url) => Container(
                            color: Colors.grey[200],
                            child: const Icon(
                              Icons.person,
                              color: Colors.grey,
                              size: 30,
                            ),
                          ),
                          errorWidget: (context, url, error) => Container(
                            color: Colors.grey[200],
                            child: const Icon(
                              Icons.person,
                              color: Colors.grey,
                              size: 30,
                            ),
                          ),
                        )
                      : Container(
                          color: Colors.grey[200],
                          child: const Icon(
                            Icons.person,
                            color: Colors.grey,
                            size: 30,
                          ),
                        ),
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Node Information
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Name
                    Text(
                      node.name,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    const SizedBox(height: 4),
                    
                    // Nickname (if available)
                    if (node.nickName != null && node.nickName!.isNotEmpty)
                      Text(
                        'الكنية: ${node.nickName}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).primaryColor,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    
                    const SizedBox(height: 4),
                    
                    // Status and basic info
                    Row(
                      children: [
                        // Alive status
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: node.alive == 1 
                                ? Colors.green.withOpacity(0.1)
                                : Colors.grey.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            node.alive == 1 ? 'على قيد الحياة' : 'متوفى',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: node.alive == 1 ? Colors.green : Colors.grey,
                              fontSize: 10,
                            ),
                          ),
                        ),
                        
                        const SizedBox(width: 8),
                        
                        // Gender
                        if (node.gender != null)
                          Icon(
                            node.gender == 1 ? Icons.male : Icons.female,
                            size: 16,
                            color: node.gender == 1 ? Colors.blue : Colors.pink,
                          ),
                      ],
                    ),
                    
                    // Job (if available)
                    if (node.job != null && node.job!.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        node.job!,
                        style: Theme.of(context).textTheme.bodySmall,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),
              
              // Selection indicator
              if (showSelectionIndicator)
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isSelected 
                          ? Theme.of(context).primaryColor
                          : Colors.grey,
                      width: 2,
                    ),
                    color: isSelected 
                        ? Theme.of(context).primaryColor
                        : Colors.transparent,
                  ),
                  child: isSelected
                      ? const Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 16,
                        )
                      : null,
                ),
            ],
          ),
        ),
      ),
    );
  }
}

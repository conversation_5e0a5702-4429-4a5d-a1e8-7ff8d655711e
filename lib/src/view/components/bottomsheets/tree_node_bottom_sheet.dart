import 'package:almashal/src/core/routes/app_pages.dart';
import 'package:almashal/src/core/utils/common_functions.dart';
import 'package:almashal/src/data/models/family_tree_node.dart';
import 'package:almashal/src/data/models/user.dart';
import 'package:almashal/src/data/services/auth_service.dart';
import 'package:almashal/src/view/components/bottomsheets/add_tree_change_bottom_sheet.dart';
import 'package:almashal/src/view/components/image/custom_cached_network_image.dart';
import 'package:almashal/src/view/pages/profile/widget/empty_state.dart';
import 'package:almashal/src/controllers/profile/reviews_controller.dart';
import 'package:almashal/src/data/models/profile/review.dart';
import 'package:almashal/src/view/components/form_fields/app_text_form_field.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:url_launcher/url_launcher.dart';

class TreeNodeBottomSheet extends StatefulWidget {
  const TreeNodeBottomSheet({
    super.key,
    required this.node,
  });
  final FamilyTreeNode node;

  @override
  State<TreeNodeBottomSheet> createState() => _TreeNodeBottomSheetState();
}

class _TreeNodeBottomSheetState extends State<TreeNodeBottomSheet>
    with SingleTickerProviderStateMixin {
  User? _familyMemberUser;
  bool _isLoadingUser = false;
  TabController? _tabController;
  ReviewsController? _reviewsController;
  @override
  void initState() {
    super.initState();
    _loadFamilyMemberData();
  }

  void _initializeTabController() {
    // Always create TabController with 7 tabs (all available tabs including Reviews)
    if (_tabController == null || _tabController!.length != 7) {
      _tabController?.dispose();
      _tabController = TabController(length: 7, vsync: this);
    }
  }

  @override
  void dispose() {
    _tabController?.dispose();
    // Clean up reviews controller
    if (_reviewsController != null) {
      Get.delete<ReviewsController>(tag: 'tree_node_${widget.node.id}');
    }
    super.dispose();
  }

  Future<void> _loadFamilyMemberData() async {
    if (widget.node.familyMemberId != null) {
      setState(() {
        _isLoadingUser = true;
      });

      try {
        final user = await AuthService.instance
            .getPublicProfile(widget.node.familyMemberId.toString());

        // Initialize reviews controller
        _reviewsController =
            Get.put(ReviewsController(), tag: 'tree_node_${widget.node.id}');
        _reviewsController!.setUserId(user.id.toString());

        setState(() {
          _familyMemberUser = user;
          _isLoadingUser = false;
        });
      } catch (e) {
        setState(() {
          _isLoadingUser = false;
        });
      }
    }
  }

  // URL Launcher utility methods
  Future<void> _launchUrl(String url, {String? errorMessage}) async {
    try {
      // Validate URL format
      if (url.isEmpty) {
        _showErrorSnackbar(errorMessage ?? 'الرابط فارغ');
        return;
      }

      // Add protocol if missing
      String formattedUrl = url;
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        formattedUrl = 'https://$url';
      }

      final uri = Uri.parse(formattedUrl);

      // Check if URL can be launched
      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
      } else {
        _showErrorSnackbar(errorMessage ?? 'لا يمكن فتح الرابط');
      }
    } catch (e) {
      _showErrorSnackbar(errorMessage ?? 'حدث خطأ أثناء فتح الرابط');
    }
  }

  Future<void> _launchFile(String fileUrl, {String? fileName}) async {
    try {
      if (fileUrl.isEmpty) {
        _showErrorSnackbar('رابط الملف فارغ');
        return;
      }

      final uri = Uri.parse(fileUrl);

      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
      } else {
        _showErrorSnackbar('لا يمكن فتح الملف');
      }
    } catch (e) {
      _showErrorSnackbar('حدث خطأ أثناء فتح الملف');
    }
  }

  void _showErrorSnackbar(String message) {
    Get.snackbar(
      'خطأ',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.red,
      colorText: Colors.white,
      duration: const Duration(seconds: 3),
    );
  }

  void _showSuccessSnackbar(String message) {
    Get.snackbar(
      'نجح',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green,
      colorText: Colors.white,
      duration: const Duration(seconds: 2),
    );
  }

  Widget _buildProfileContent() {
    // If we have family member data, show profile-style information
    if (_familyMemberUser != null) {
      return _buildFamilyMemberProfile();
    }

    // If loading user data, show loading indicator
    if (_isLoadingUser) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(20.0),
          child: CircularProgressIndicator(),
        ),
      );
    }

    // Otherwise, show tree node information using TreeTextView
    return _buildTreeNodeInfo();
  }

  Widget _buildFamilyMemberProfile() {
    final user = _familyMemberUser;

    // Safety check - should not happen but prevents null pointer exceptions
    if (user == null) {
      return const Center(
        child: Text('خطأ في تحميل بيانات المستخدم'),
      );
    }

    // Build tabs list
    final tabs = _buildTabsList(user);
    final tabViews = _buildTabViewsList(user);

    // Initialize TabController with 7 tabs (always)
    _initializeTabController();

    // Safety check for TabController
    if (_tabController == null) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Tab Bar
        Container(
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(12),
          ),
          child: TabBar(
            controller: _tabController!,
            isScrollable: true,
            labelColor: Get.theme.primaryColor,
            unselectedLabelColor: Colors.grey,
            indicatorColor: Get.theme.primaryColor,
            indicatorWeight: 3,
            tabAlignment: TabAlignment.start,
            labelStyle: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
            unselectedLabelStyle: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.normal,
            ),
            tabs: tabs,
          ),
        ),

        const SizedBox(height: 16),

        // Tab Bar View with fixed height
        SizedBox(
          height: 400, // Fixed height for TabBarView
          child: TabBarView(
            controller: _tabController!,
            children: tabViews,
          ),
        ),
      ],
    );
  }

  List<Tab> _buildTabsList(User user) {
    try {
      // Always return all 7 tabs regardless of data availability
      return [
        Tab(
          icon: Icon(Icons.person, size: 20),
          text: 'المعلومات الأساسية',
        ),
        Tab(
          icon: Icon(Icons.description, size: 20),
          text: 'السيرة الذاتية',
        ),
        Tab(
          icon: Icon(Icons.star, size: 20),
          text: 'المهارات',
        ),
        Tab(
          icon: Icon(Icons.work, size: 20),
          text: 'الخبرات',
        ),
        Tab(
          icon: Icon(Icons.emoji_events, size: 20),
          text: 'الإنجازات',
        ),
        Tab(
          icon: Icon(Icons.share, size: 20),
          text: 'التواصل',
        ),
        Tab(
          icon: Icon(Icons.rate_review, size: 20),
          text: 'التقييمات',
        ),
      ];
    } catch (e) {
      // Return all tabs even if there's an error
      return [
        Tab(
          icon: Icon(Icons.person, size: 20),
          text: 'المعلومات الأساسية',
        ),
        Tab(
          icon: Icon(Icons.description, size: 20),
          text: 'السيرة الذاتية',
        ),
        Tab(
          icon: Icon(Icons.star, size: 20),
          text: 'المهارات',
        ),
        Tab(
          icon: Icon(Icons.work, size: 20),
          text: 'الخبرات',
        ),
        Tab(
          icon: Icon(Icons.emoji_events, size: 20),
          text: 'الإنجازات',
        ),
        Tab(
          icon: Icon(Icons.share, size: 20),
          text: 'التواصل',
        ),
        Tab(
          icon: Icon(Icons.rate_review, size: 20),
          text: 'التقييمات',
        ),
      ];
    }
  }

  List<Widget> _buildTabViewsList(User user) {
    try {
      // Always return all 7 tab views regardless of data availability
      return [
        _buildBasicInfoTab(user),
        _hasCVData(user) ? _buildCVTab(user) : _buildEmptyCVTab(),
        user.skills.isNotEmpty
            ? _buildSkillsTab(user.skills)
            : _buildEmptySkillsTab(),
        user.experiences.isNotEmpty
            ? _buildExperienceTab(user.experiences)
            : _buildEmptyExperienceTab(),
        user.achievements.isNotEmpty
            ? _buildAchievementsTab(user.achievements)
            : _buildEmptyAchievementsTab(),
        _hasSocialMediaLinks(user)
            ? _buildSocialMediaTab(user)
            : _buildEmptySocialMediaTab(),
        _buildReviewsTab(user),
      ];
    } catch (e) {
      // Return all tab views with empty states if there's an error
      return [
        _buildBasicInfoTab(user),
        _buildEmptyCVTab(),
        _buildEmptySkillsTab(),
        _buildEmptyExperienceTab(),
        _buildEmptyAchievementsTab(),
        _buildEmptySocialMediaTab(),
        _buildEmptyReviewsTab(),
      ];
    }
  }

  Widget _buildTreeNodeInfo() {
    return Column(
      children: [
        TreeTextView(
          title: 'نبذه:',
          subtitle: widget.node.overview,
        ),
        TreeTextView(
          title: 'الكنية:',
          subtitle: widget.node.nickName,
        ),
        TreeTextView(
          title: 'الوظيفة:',
          subtitle: widget.node.job,
        ),
        TreeTextView(
          title: 'عنوان الاقامة:',
          subtitle: widget.node.address,
        ),
        TreeTextView(
          title: 'تاريخ الميلاد:',
          subtitle: widget.node.birthDate,
        ),
        TreeTextView(
          title: 'مكان الميلاد:',
          subtitle: widget.node.birthPlace,
        ),
        if (widget.node.alive != 1)
          TreeTextView(
            title: 'تاريخ الوفاه:',
            subtitle: widget.node.deathDate,
          ),
        if (widget.node.alive != 1)
          TreeTextView(
            title: 'مكان الوفاه:',
            subtitle: widget.node.deathPlace,
          ),
      ],
    );
  }

  Widget _buildProfileInfoCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, size: 20, color: Get.theme.primaryColor),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Get.theme.primaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildProfileInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                color: Colors.black54,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods for checking data availability
  bool _hasCVData(User user) {
    try {
      return user.cvType != null ||
          (user.cvText?.isNotEmpty == true) ||
          (user.cvFile?.isNotEmpty == true);
    } catch (e) {
      return false;
    }
  }

  bool _hasSocialMediaLinks(User user) {
    try {
      return (user.facebookLink?.isNotEmpty == true) ||
          (user.xLink?.isNotEmpty == true) ||
          (user.instagramLink?.isNotEmpty == true) ||
          (user.linkedinLink?.isNotEmpty == true) ||
          (user.youtubeLink?.isNotEmpty == true) ||
          (user.tiktokLink?.isNotEmpty == true) ||
          (user.snapshotLink?.isNotEmpty == true);
    } catch (e) {
      return false;
    }
  }

  Widget _buildSkillItem(dynamic skill) {
    // Null safety checks
    final title = skill?.title ?? 'مهارة غير محددة';
    final level = skill?.level ?? 0;
    final description = skill?.description ?? '';

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
              _buildSkillLevel(level),
            ],
          ),
          if (description.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              description,
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 14,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSkillLevel(int level) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(5, (index) {
        return Icon(
          index < level ? Icons.star : Icons.star_border,
          color: index < level ? Colors.amber : Colors.grey,
          size: 16,
        );
      }),
    );
  }

  Widget _buildExperienceItem(dynamic experience) {
    // Null safety checks
    final title = experience?.title ?? 'خبرة غير محددة';
    final organization = experience?.organization ?? 'منظمة غير محددة';
    final description = experience?.description ?? '';
    final location = experience?.location ?? '';
    final typeLabel = experience?.typeLabel ?? 'نوع غير محدد';
    final isCurrentPosition = experience?.isCurrentPosition ?? false;

    String startDate = 'غير محدد';
    String endDate = 'حتى الآن';

    try {
      if (experience?.startDate != null) {
        startDate = DateFormat('yyyy/MM/dd').format(experience.startDate);
      }
      if (experience?.endDate != null) {
        endDate = DateFormat('yyyy/MM/dd').format(experience.endDate);
      }
    } catch (e) {
      // Handle date formatting errors gracefully
      startDate = 'تاريخ غير صحيح';
      endDate = 'تاريخ غير صحيح';
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Get.theme.primaryColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Get.theme.primaryColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: Get.theme.primaryColor,
                  ),
                ),
              ),
              if (isCurrentPosition)
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'حالي',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            organization,
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 15,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '$startDate - $endDate',
            style: TextStyle(
              color: Colors.grey.shade600,
              fontSize: 14,
            ),
          ),
          if (location.isNotEmpty) ...[
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(Icons.location_on, size: 16, color: Colors.grey.shade600),
                const SizedBox(width: 4),
                Text(
                  location,
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ],
          if (description.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              description,
              style: const TextStyle(fontSize: 14),
            ),
          ],
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.orange.shade100,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              typeLabel,
              style: TextStyle(
                color: Colors.orange.shade800,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAchievementItem(dynamic achievement) {
    // Null safety checks
    final title = achievement?.title ?? 'إنجاز غير محدد';
    final description = achievement?.description ?? '';
    final type = achievement?.type ?? 'نوع غير محدد';
    final certificateUrl = achievement?.certificateUrl ?? '';
    final imageUrl = achievement?.imageUrl ?? '';

    String achievementDate = 'تاريخ غير محدد';
    try {
      if (achievement?.date != null) {
        achievementDate = DateFormat('yyyy/MM/dd').format(achievement.date);
      }
    } catch (e) {
      achievementDate = 'تاريخ غير صحيح';
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.amber.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.amber.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.emoji_events, color: Colors.amber.shade700, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: Colors.amber.shade800,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            achievementDate,
            style: TextStyle(
              color: Colors.grey.shade600,
              fontSize: 14,
            ),
          ),
          if (description.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              description,
              style: const TextStyle(fontSize: 14),
            ),
          ],
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.purple.shade100,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              type,
              style: TextStyle(
                color: Colors.purple.shade800,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          if (certificateUrl.isNotEmpty || imageUrl.isNotEmpty) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                if (certificateUrl.isNotEmpty)
                  Icon(Icons.file_present,
                      size: 16, color: Colors.grey.shade600),
                if (imageUrl.isNotEmpty)
                  Icon(Icons.image, size: 16, color: Colors.grey.shade600),
                const SizedBox(width: 4),
                Text(
                  'يحتوي على مرفقات',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSocialMediaLink(
      String platform, String url, IconData icon, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: () => _launchUrl(url, errorMessage: 'لا يمكن فتح $platform'),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Row(
              children: [
                Icon(icon, color: color, size: 20),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        platform,
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                        ),
                      ),
                      Text(
                        url,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                          decoration: TextDecoration.underline,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                Icon(Icons.open_in_new, size: 16, color: Colors.grey.shade600),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Helper widgets for CV section
  Widget _buildExpandableText(String label, String text) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '$label:',
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.shade200),
          ),
          child: Text(
            text,
            style: const TextStyle(
              color: Colors.black54,
              fontSize: 14,
            ),
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildFileRow(String label, String fileName, String? fileUrl) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: fileUrl != null && fileUrl.isNotEmpty
              ? () => _launchFile(fileUrl)
              : null,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade200),
              color: fileUrl != null && fileUrl.isNotEmpty
                  ? Colors.grey.shade50
                  : Colors.grey.shade100,
            ),
            child: Row(
              children: [
                Icon(
                  Icons.file_present,
                  color: fileUrl != null && fileUrl.isNotEmpty
                      ? Get.theme.primaryColor
                      : Colors.grey,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        label,
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                        ),
                      ),
                      Text(
                        fileName,
                        style: TextStyle(
                          color: fileUrl != null && fileUrl.isNotEmpty
                              ? Colors.blue.shade600
                              : Colors.grey.shade600,
                          fontSize: 12,
                          decoration: fileUrl != null && fileUrl.isNotEmpty
                              ? TextDecoration.underline
                              : null,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                Icon(
                  fileUrl != null && fileUrl.isNotEmpty
                      ? Icons.open_in_new
                      : Icons.file_download_off,
                  size: 16,
                  color: fileUrl != null && fileUrl.isNotEmpty
                      ? Colors.grey.shade600
                      : Colors.grey.shade400,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Tab Methods for TabBarView
  Widget _buildBasicInfoTab(User user) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Basic Information Card
          _buildProfileInfoCard(
            title: 'المعلومات الأساسية',
            icon: Icons.person,
            children: [
              if (user.overview?.isNotEmpty == true)
                _buildProfileInfoRow('نبذة', user.overview!),
              _buildProfileInfoRow('الاسم الكامل', user.displayName),
              _buildProfileInfoRow('الجنس', user.gender == 1 ? 'ذكر' : 'أنثى'),
              if (user.birthDate != null)
                _buildProfileInfoRow('تاريخ الميلاد',
                    '${user.birthDate!.day}/${user.birthDate!.month}/${user.birthDate!.year}'),
              if (user.birthPlace?.isNotEmpty == true)
                _buildProfileInfoRow('مكان الميلاد', user.birthPlace!),
            ],
          ),

          const SizedBox(height: 16),

          // Contact Information Card
          _buildProfileInfoCard(
            title: 'معلومات الاتصال',
            icon: Icons.contact_phone,
            children: [
              if (user.email.isNotEmpty)
                _buildProfileInfoRow('البريد الإلكتروني', user.email),
              if (user.address?.isNotEmpty == true)
                _buildProfileInfoRow('العنوان', user.address!),
              _buildProfileInfoRow('المدينة', user.city),
              _buildProfileInfoRow('الدولة', user.country),
            ],
          ),

          // Family Information Card
          if (user.branch?.isNotEmpty == true) ...[
            const SizedBox(height: 16),
            _buildProfileInfoCard(
              title: 'معلومات العائلة',
              icon: Icons.family_restroom,
              children: [
                _buildProfileInfoRow('الفرع', user.branch!),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCVTab(User user) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: _buildProfileInfoCard(
        title: 'السيرة الذاتية',
        icon: Icons.description,
        children: [
          if (user.cvType != null)
            _buildProfileInfoRow('نوع السيرة الذاتية', user.cvType!),
          if (user.cvText?.isNotEmpty == true)
            _buildExpandableText('نص السيرة الذاتية', user.cvText!),
          if (user.cvFile?.isNotEmpty == true)
            _buildFileRow('ملف السيرة الذاتية', user.cvFile!, user.cvFileUrl),
        ],
      ),
    );
  }

  Widget _buildSkillsTab(List skills) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: skills.map((skill) => _buildSkillItem(skill)).toList(),
      ),
    );
  }

  Widget _buildExperienceTab(List experiences) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: experiences.map((exp) => _buildExperienceItem(exp)).toList(),
      ),
    );
  }

  Widget _buildAchievementsTab(List achievements) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: achievements
            .map((achievement) => _buildAchievementItem(achievement))
            .toList(),
      ),
    );
  }

  Widget _buildSocialMediaTab(User user) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: _buildProfileInfoCard(
        title: 'وسائل التواصل الاجتماعي',
        icon: Icons.share,
        children: [
          if (user.facebookLink?.isNotEmpty == true)
            _buildSocialMediaLink('فيسبوك', user.facebookLink!,
                FontAwesomeIcons.facebook, Get.theme.primaryColor),
          if (user.instagramLink?.isNotEmpty == true)
            _buildSocialMediaLink('إنستغرام', user.instagramLink!,
                FontAwesomeIcons.instagram, Colors.pink),
          if (user.xLink?.isNotEmpty == true)
            _buildSocialMediaLink('تويتر', user.xLink!,
                FontAwesomeIcons.twitter, Colors.lightBlue),
          if (user.linkedinLink?.isNotEmpty == true)
            _buildSocialMediaLink('لينكد إن', user.linkedinLink!,
                FontAwesomeIcons.linkedin, Colors.blue),
          if (user.youtubeLink?.isNotEmpty == true)
            _buildSocialMediaLink('يوتيوب', user.youtubeLink!,
                FontAwesomeIcons.youtube, Colors.red),
          if (user.tiktokLink?.isNotEmpty == true)
            _buildSocialMediaLink('تيك توك', user.tiktokLink!,
                FontAwesomeIcons.tiktok, Colors.black),
          if (user.snapshotLink?.isNotEmpty == true)
            _buildSocialMediaLink('سناب شات', user.snapshotLink!,
                FontAwesomeIcons.snapchat, Colors.yellow),
        ],
      ),
    );
  }

  Widget _buildReviewsTab(User user) {
    if (_reviewsController == null) {
      return _buildEmptyReviewsTab();
    }

    return Obx(() {
      if (_reviewsController!.isLoading.value) {
        return const Center(
          child: Padding(
            padding: EdgeInsets.all(20.0),
            child: CircularProgressIndicator(),
          ),
        );
      }

      return Column(
        children: [
          // Review Summary
          if (_reviewsController!.reviewSummary.value != null)
            _buildReviewSummaryCard(_reviewsController!.reviewSummary.value!),

          // Add Review Button (only if user is authenticated and not reviewing themselves)
          if (AuthService.instance.isAuthenticated() &&
              AuthService.instance.userData.value?.user.id != user.id)
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () => _showAddReviewDialog(user),
                  icon: const Icon(Icons.add_comment),
                  label: const Text('إضافة تقييم'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Get.theme.primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ),

          // Reviews List
          Expanded(
            child: _reviewsController!.reviewsList.isEmpty
                ? _buildEmptyReviewsTab()
                : ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: _reviewsController!.reviewsList.length,
                    itemBuilder: (context, index) {
                      final review = _reviewsController!.reviewsList[index];
                      return _buildReviewCard(review);
                    },
                  ),
          ),
        ],
      );
    });
  }

  // Review-related helper methods
  Widget _buildReviewSummaryCard(dynamic reviewSummary) {
    // Safety checks for review summary data
    final reviewsCount = reviewSummary?.reviewsCount ?? 0;
    final averageRating = reviewSummary?.averageRating ?? 0.0;
    final ratingsBreakdown = reviewSummary?.ratingsBreakdown ?? <String, int>{};

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildSummaryItem(
                  'إجمالي التقييمات',
                  reviewsCount.toString(),
                  Icons.rate_review,
                ),
                _buildSummaryItem(
                  'متوسط التقييم',
                  averageRating.isFinite
                      ? averageRating.toStringAsFixed(1)
                      : '0.0',
                  Icons.star,
                ),
              ],
            ),
            if (ratingsBreakdown.isNotEmpty) ...[
              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 16),
              _buildRatingsBreakdown(ratingsBreakdown),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(String title, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, size: 32, color: Get.theme.primaryColor),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          title,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  Widget _buildRatingsBreakdown(Map<String, int> breakdown) {
    // Calculate total count safely
    final totalCount =
        breakdown.values.fold<int>(0, (sum, count) => sum + count);

    // Return empty widget if no data
    if (totalCount == 0) {
      return const SizedBox.shrink();
    }

    return Column(
      children: breakdown.entries.map((entry) {
        final rating = int.tryParse(entry.key) ?? 0;
        final count = entry.value;

        // Calculate progress value safely
        double progressValue = 0.0;
        if (totalCount > 0 && count > 0) {
          progressValue = count / totalCount;
          // Ensure the value is between 0.0 and 1.0
          progressValue = progressValue.clamp(0.0, 1.0);
        }

        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 2),
          child: Row(
            children: [
              Text('$rating ⭐'),
              const SizedBox(width: 8),
              Expanded(
                child: LinearProgressIndicator(
                  value: progressValue,
                  backgroundColor: Colors.grey.shade300,
                  valueColor:
                      AlwaysStoppedAnimation<Color>(Get.theme.primaryColor),
                ),
              ),
              const SizedBox(width: 8),
              Text('$count'),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildReviewCard(Review review) {
    final currentUserId = AuthService.instance.userData.value?.user.id;
    final canEditDelete =
        currentUserId != null && review.isReviewerCurrentUser(currentUserId);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundColor: Get.theme.primaryColor,
                  child: Text(
                    review.reviewer.displayName.isNotEmpty
                        ? review.reviewer.displayName[0].toUpperCase()
                        : '؟',
                    style: const TextStyle(
                        color: Colors.white, fontWeight: FontWeight.bold),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        review.reviewer.displayName,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        review.createdAtFormatted.isNotEmpty
                            ? review.createdAtFormatted
                            : review.createdAt,
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                // Rating stars
                Row(
                  children: List.generate(5, (index) {
                    return Icon(
                      index < review.rating ? Icons.star : Icons.star_border,
                      color: Colors.amber,
                      size: 20,
                    );
                  }),
                ),
                if (canEditDelete)
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      if (value == 'edit') {
                        _showEditReviewDialog(review);
                      } else if (value == 'delete') {
                        _showDeleteConfirmation(review);
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit, size: 16),
                            SizedBox(width: 8),
                            Text('تعديل'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, size: 16, color: Colors.red),
                            SizedBox(width: 8),
                            Text('حذف', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                  ),
              ],
            ),
            if (review.comment.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                review.comment,
                style: const TextStyle(fontSize: 14),
              ),
            ],
          ],
        ),
      ),
    );
  }

  // Empty state methods for tabs with no data
  Widget _buildEmptyCVTab() {
    return const EmptyState(
      message: 'لا توجد سيرة ذاتية مضافة',
    );
  }

  Widget _buildEmptySkillsTab() {
    return const EmptyState(
      message: 'لا توجد مهارات مضافة',
    );
  }

  Widget _buildEmptyExperienceTab() {
    return const EmptyState(
      message: 'لا توجد خبرات مضافة',
    );
  }

  Widget _buildEmptyAchievementsTab() {
    return const EmptyState(
      message: 'لا توجد إنجازات مضافة',
    );
  }

  Widget _buildEmptySocialMediaTab() {
    return const EmptyState(
      message: 'لا توجد روابط تواصل اجتماعي مضافة',
    );
  }

  Widget _buildEmptyReviewsTab() {
    return const EmptyState(
      message: 'لا توجد تقييمات مضافة',
    );
  }

  // Review Dialog Methods
  Future<void> _showAddReviewDialog(User user) async {
    double rating = 5.0;
    final commentController = TextEditingController();
    final formKey = GlobalKey<FormState>();

    await showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('إضافة تقييم'),
          content: SingleChildScrollView(
            child: Form(
              key: formKey,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // User info
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 20,
                        backgroundColor: Get.theme.primaryColor,
                        child: Text(
                          user.displayName.isNotEmpty
                              ? user.displayName[0].toUpperCase()
                              : '؟',
                          style: const TextStyle(
                              color: Colors.white, fontWeight: FontWeight.bold),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'تقييم ${user.displayName}',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),

                  // Rating slider
                  Text(
                    'التقييم: ${rating.round()} نجوم',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: List.generate(5, (index) {
                      return Icon(
                        index < rating.round() ? Icons.star : Icons.star_border,
                        color: Colors.amber,
                        size: 30,
                      );
                    }),
                  ),
                  Slider(
                    value: rating,
                    onChanged: (value) {
                      setState(() {
                        rating = value;
                      });
                    },
                    min: 1,
                    max: 5,
                    divisions: 4,
                    activeColor: Get.theme.primaryColor,
                  ),
                  const SizedBox(height: 16),

                  // Comment field
                  AppTextFormField(
                    controller: commentController,
                    labelText: 'التعليق (اختياري)',
                    hintText: 'اكتب تعليقك هنا...',
                    maxLines: 4,
                    validator: null, // Comment is optional
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                if (formKey.currentState?.validate() ?? false) {
                  _reviewsController!.addReview({
                    'user_id': user.id,
                    'rating': rating.round(),
                    'comment': commentController.text,
                  });
                  Navigator.of(context).pop();
                  _showSuccessSnackbar('تم إضافة التقييم بنجاح');
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Get.theme.primaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('حفظ التقييم'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _showEditReviewDialog(Review review) async {
    double rating = review.rating.toDouble();
    final commentController = TextEditingController(text: review.comment);
    final formKey = GlobalKey<FormState>();

    await showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('تعديل التقييم'),
          content: SingleChildScrollView(
            child: Form(
              key: formKey,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Rating slider
                  Text(
                    'التقييم: ${rating.round()} نجوم',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: List.generate(5, (index) {
                      return Icon(
                        index < rating.round() ? Icons.star : Icons.star_border,
                        color: Colors.amber,
                        size: 30,
                      );
                    }),
                  ),
                  Slider(
                    value: rating,
                    onChanged: (value) {
                      setState(() {
                        rating = value;
                      });
                    },
                    min: 1,
                    max: 5,
                    divisions: 4,
                    activeColor: Get.theme.primaryColor,
                  ),
                  const SizedBox(height: 16),

                  // Comment field
                  AppTextFormField(
                    controller: commentController,
                    labelText: 'التعليق (اختياري)',
                    hintText: 'اكتب تعليقك هنا...',
                    maxLines: 4,
                    validator: null, // Comment is optional
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                if (formKey.currentState?.validate() ?? false) {
                  _reviewsController!.updateReview(review.id, {
                    'rating': rating.round(),
                    'comment': commentController.text,
                  });
                  Navigator.of(context).pop();
                  _showSuccessSnackbar('تم تحديث التقييم بنجاح');
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Get.theme.primaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('حفظ التغييرات'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _showDeleteConfirmation(Review review) async {
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف التقييم'),
        content: const Text('هل أنت متأكد من رغبتك في حذف هذا التقييم؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              _reviewsController!.deleteReview(review.id);
              Navigator.of(context).pop();
              _showSuccessSnackbar('تم حذف التقييم بنجاح');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    var name = widget.node.name;
    if (widget.node.parent != null && widget.node.gender == 1) {
      name += " بن ${widget.node.parent?.name ?? ""}";
    }

    if (widget.node.parent != null && widget.node.gender == 2) {
      name += " بنت ${widget.node.parent?.name ?? ""}";
    }

    // if (widget.node.nickName != null) {
    //   name += " (${widget.node!.nickName})";
    // }
    if (widget.node.alive != 1 && widget.node.gender == 1) {
      name += " (رحمه الله)";
    }

    if (widget.node.alive != 1 && widget.node.gender == 2) {
      name += " (رحمها الله)";
    }

    return Container(
      width: double.maxFinite,
      constraints: BoxConstraints(
        maxHeight: Get.height * 0.9,
        minHeight: 100,
      ),
      decoration: const BoxDecoration(
        color: Colors.transparent,
      ),
      child: Stack(
        clipBehavior: Clip.none,
        fit: StackFit.loose,
        children: [
          Container(
            height: double.maxFinite,
            constraints: BoxConstraints(
              maxHeight: Get.height * 0.9,
              minHeight: 100,
            ),
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(
                top: Radius.circular(16),
              ),
            ),
          ),
          Positioned(
            right: 0,
            left: 0,
            top: -30,
            bottom: 0,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.all(1),
                  decoration: BoxDecoration(
                    border: Border.all(
                      width: 2,
                      color: widget.node.gender == 1
                          ? Theme.of(context).primaryColor
                          : Colors.pink,
                      strokeAlign: BorderSide.strokeAlignOutside,
                    ),
                    borderRadius: BorderRadius.circular(50),
                  ),
                  child: CustomCachedNetworkImage(
                    imageUrl: widget.node.image ?? '',
                    thumbImageUrl: widget.node.thumbImageUrl ?? '',
                    height: 70,
                    width: 70,
                    fit: BoxFit.cover,
                    withPreview: true,
                    borderRadius: BorderRadius.circular(
                      100,
                    ),
                  ),
                ),
                const SizedBox(
                  height: 8,
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(
                    name,
                    style: const TextStyle(
                      fontSize: 17,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Expanded(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: _buildProfileContent(),
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Column(
                    children: [
                      // الصف الأول: زر الملف الشخصي وزر إضافة ابن
                      Row(
                        children: [
                          // Connection request button
                          if (AuthService.instance.isAuthenticated() &&
                              widget.node.familyMemberId == null)
                            Expanded(
                              child: FilledButton(
                                style: ButtonStyle(
                                  backgroundColor: WidgetStateProperty.all(
                                    Colors.orange,
                                  ),
                                ),
                                onPressed: () {
                                  Get.back();
                                  Get.toNamed(
                                    Routes.CONNECTION_REQUEST_FORM_PAGE,
                                    arguments: widget.node,
                                  );
                                },
                                child: const FittedBox(
                                    child: Text("ربط مع ملفي الشخصي")),
                              ),
                            ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: FilledButton(
                              style: ButtonStyle(
                                backgroundColor: WidgetStateProperty.all(
                                  Colors.blue,
                                ),
                              ),
                              onPressed: () {
                                int value = 1;
                                if (!AuthService.instance.isAuthenticated()) {
                                  CommonFunctions.showErrorMessage(
                                      'يرجى تسجيل الدخول للمتابعة');
                                  Get.toNamed(Routes.LOGIN_PAGE);
                                } else {
                                  Get.bottomSheet(
                                    AddTreeChangeRequest(
                                      actionType: value,
                                      node: widget.node,
                                    ),
                                  );
                                }
                              },
                              child: const Text("اضافة ابن"),
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 8), // فاصل بين الصفين

                      // الصف الثاني: زر طلب تعديل وزر طلب حذف
                      Row(
                        children: [
                          Expanded(
                            child: FilledButton(
                              style: ButtonStyle(
                                backgroundColor: WidgetStateProperty.all(
                                  Colors.green,
                                ),
                              ),
                              onPressed: () {
                                int value = 2;
                                if (!AuthService.instance.isAuthenticated()) {
                                  CommonFunctions.showErrorMessage(
                                      'يرجى تسجيل الدخول للمتابعة');
                                  Get.toNamed(Routes.LOGIN_PAGE);
                                } else {
                                  Get.bottomSheet(
                                    AddTreeChangeRequest(
                                      actionType: value,
                                      node: widget.node,
                                    ),
                                  );
                                }
                              },
                              child: const Text("طلب تعديل"),
                            ),
                          ),
                          const SizedBox(
                            width: 4,
                          ),
                          Expanded(
                            child: FilledButton(
                              style: ButtonStyle(
                                backgroundColor: WidgetStateProperty.all(
                                  Colors.red,
                                ),
                              ),
                              onPressed: () {
                                int value = 0;
                                if (!AuthService.instance.isAuthenticated()) {
                                  CommonFunctions.showErrorMessage(
                                      'يرجى تسجيل الدخول للمتابعة');
                                  Get.toNamed(Routes.LOGIN_PAGE);
                                } else {
                                  Get.bottomSheet(
                                    AddTreeChangeRequest(
                                      actionType: value,
                                      node: widget.node,
                                    ),
                                  );
                                }
                              },
                              child: const Text("طلب حذف"),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                )
              ],
            ),
          ),
          Positioned(
            top: 0,
            left: 8,
            child: IconButton(
              onPressed: () => Get.back(),
              icon: const Icon(Icons.close),
            ),
          ),
        ],
      ),
    );
  }
}

class TreeTextView extends StatelessWidget {
  const TreeTextView({
    super.key,
    required this.title,
    this.subtitle,
    this.subtitleColor,
  });

  final String title;
  final String? subtitle;
  final Color? subtitleColor;

  @override
  Widget build(BuildContext context) {
    if (subtitle == null) {
      return const SizedBox();
    }
    return SizedBox(
      width: double.maxFinite,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            textAlign: TextAlign.start,
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: Theme.of(context).primaryColor,
              fontSize: 15,
            ),
          ),
          const SizedBox(
            height: 4,
          ),
          Text(
            subtitle ?? "",
            textAlign: TextAlign.start,
            style: TextStyle(
              fontSize: 14,
              color: subtitleColor ?? Colors.black54,
            ),
          ),
          Divider(
            height: 24,
            thickness: 0.4,
            color: Theme.of(context).primaryColor,
            endIndent: 50,
          ),
        ],
      ),
    );
  }
}

import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../change_avatar_form_field_with_cropping.dart';

/// Example demonstrating how to use ChangeAvatarFormFieldWithCropping
/// in a Flutter form with proper form validation and submission.
class AvatarFormFieldExample extends StatefulWidget {
  const AvatarFormFieldExample({super.key});

  @override
  State<AvatarFormFieldExample> createState() => _AvatarFormFieldExampleState();
}

class _AvatarFormFieldExampleState extends State<AvatarFormFieldExample> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  File? _selectedAvatarFile;

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Avatar Form Field Example'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const Text(
                'Profile Setup',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),

              // Avatar Form Field with Cropping
              Center(
                child: ChangeAvatarFormFieldWithCropping(
                  // Optional: Set initial image URL if editing existing profile
                  imageUrl: 'https://example.com/existing-avatar.jpg',

                  // Form validation
                  validator: (File? value) {
                    if (value == null) {
                      return 'Please select a profile image';
                    }
                    return null;
                  },

                  // Called when form.save() is invoked
                  onSaved: (File? croppedImage) {
                    _selectedAvatarFile = croppedImage;
                    print('Avatar saved: ${croppedImage?.path}');
                  },

                  // Called whenever the image changes (real-time)
                  onChanged: (File? croppedImage) {
                    setState(() {
                      _selectedAvatarFile = croppedImage;
                    });
                    print('Avatar changed: ${croppedImage?.path}');
                  },
                ),
              ),

              const SizedBox(height: 24),

              // Display selected image info
              if (_selectedAvatarFile != null)
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      children: [
                        const Text(
                          'Selected Image:',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        Text('Path: ${_selectedAvatarFile!.path}'),
                        Text('Size: ${_selectedAvatarFile!.lengthSync()} bytes'),
                      ],
                    ),
                  ),
                ),

              const SizedBox(height: 32),

              // Name field for demonstration
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Full Name',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter your name';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 32),

              // Submit button
              ElevatedButton(
                onPressed: _submitForm,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text(
                  'Save Profile',
                  style: TextStyle(fontSize: 16),
                ),
              ),

              const SizedBox(height: 16),

              // Reset button
              OutlinedButton(
                onPressed: _resetForm,
                child: const Text('Reset Form'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _submitForm() {
    if (_formKey.currentState!.validate()) {
      // Save all form fields
      _formKey.currentState!.save();

      // Process the form data
      _processFormData();
    }
  }

  void _processFormData() {
    final name = _nameController.text.trim();

    // Show success message
    Get.snackbar(
      'Success',
      'Profile saved successfully!\nName: $name\nAvatar: ${_selectedAvatarFile != null ? 'Selected' : 'Not selected'}',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green,
      colorText: Colors.white,
      duration: const Duration(seconds: 3),
    );

    // Here you would typically:
    // 1. Upload the avatar file to your server
    // 2. Save the user data to your database
    // 3. Navigate to the next screen

    print('Form submitted:');
    print('Name: $name');
    print('Avatar file: ${_selectedAvatarFile?.path}');

    // Example: Upload avatar and save profile
    // await _uploadAvatarAndSaveProfile(name, _selectedAvatarFile);
  }

  void _resetForm() {
    _formKey.currentState!.reset();
    _nameController.clear();
    setState(() {
      _selectedAvatarFile = null;
    });

    Get.snackbar(
      'Reset',
      'Form has been reset',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.blue,
      colorText: Colors.white,
    );
  }

  // Example method for uploading avatar and saving profile
  // Future<void> _uploadAvatarAndSaveProfile(String name, File? avatarFile) async {
  //   try {
  //     String? avatarUrl;
  //
  //     if (avatarFile != null) {
  //       // Upload avatar to your server/cloud storage
  //       avatarUrl = await uploadFile(avatarFile);
  //     }
  //
  //     // Save profile data
  //     await saveUserProfile({
  //       'name': name,
  //       'avatar_url': avatarUrl,
  //     });
  //
  //     // Navigate to next screen
  //     Get.offNamed('/dashboard');
  //   } catch (e) {
  //     Get.snackbar(
  //       'Error',
  //       'Failed to save profile: $e',
  //       snackPosition: SnackPosition.BOTTOM,
  //       backgroundColor: Colors.red,
  //       colorText: Colors.white,
  //     );
  //   }
  // }
}

import 'dart:io';
import 'package:almashal/src/core/utils/platform_helper.dart';
import 'package:almashal/src/view/components/bottomsheets/custom_bottom_sheet.dart';
import 'package:almashal/src/view/components/image/custom_cached_network_image.dart';
import 'package:almashal/src/controllers/profile_controller.dart';
import 'package:feather_icons/feather_icons.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';

/// A FormField widget for avatar selection with cropping functionality
///
/// This widget extends FormField<File?> to integrate seamlessly with Flutter forms.
/// It provides image selection, cropping to square aspect ratio, and form validation.
///
/// Example usage:
/// ```dart
/// ChangeAvatarFormFieldWithCropping(
///   initialValue: null,
///   imageUrl: user.profileImageUrl,
///   onSaved: (File? croppedImage) {
///     // Handle the cropped image file
///     if (croppedImage != null) {
///       // Upload or process the image
///     }
///   },
///   validator: (File? value) {
///     if (value == null) {
///       return 'Please select a profile image';
///     }
///     return null;
///   },
///   onChanged: (File? value) {
///     // Handle real-time changes
///   },
/// )
/// ```
class ChangeAvatarFormFieldWithCropping extends FormField<File?> {
  /// Creates a new avatar form field with cropping functionality
  ChangeAvatarFormFieldWithCropping({
    super.key,
    super.onSaved,
    super.validator,
    super.initialValue,
    super.enabled = true,
    this.imageUrl,
    ValueChanged<File?>? onChanged,
  }) : super(
          builder: (FormFieldState<File?> field) {
            final widget = _ChangeAvatarFormFieldWithCroppingWidget(
              field: field,
              imageUrl: imageUrl,
              enabled: enabled,
              onChanged: onChanged,
            );
            return widget;
          },
        );

  /// Optional initial image URL to display
  final String? imageUrl;
}

/// Internal widget that handles the UI and state for the FormField
class _ChangeAvatarFormFieldWithCroppingWidget extends StatefulWidget {
  const _ChangeAvatarFormFieldWithCroppingWidget({
    required this.field,
    this.imageUrl,
    this.enabled = true,
    this.onChanged,
  });

  final FormFieldState<File?> field;
  final String? imageUrl;
  final bool enabled;
  final ValueChanged<File?>? onChanged;

  @override
  State<_ChangeAvatarFormFieldWithCroppingWidget> createState() => _ChangeAvatarFormFieldWithCroppingWidgetState();
}

class _ChangeAvatarFormFieldWithCroppingWidgetState extends State<_ChangeAvatarFormFieldWithCroppingWidget> {
  final ProfileController _profileController = Get.find<ProfileController>();
  final RxBool _isUpdating = false.obs;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Obx(() => Stack(
              children: [
                Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: _buildAvatarContent(),
                ),
                if (widget.enabled)
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.2),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          borderRadius: BorderRadius.circular(20),
                          onTap: _isUpdating.value ? null : _showImagePickerBottomSheet,
                          child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: _isUpdating.value
                                ? const SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                    ),
                                  )
                                : const Icon(
                                    FeatherIcons.edit,
                                    size: 20,
                                    color: Colors.white,
                                  ),
                          ),
                        ),
                      ),
                    ),
                  ),
              ],
            )),
        // Show validation error if any
        if (widget.field.hasError)
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Text(
              widget.field.errorText!,
              style: TextStyle(
                color: Theme.of(context).colorScheme.error,
                fontSize: 12,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildAvatarContent() {
    // Check if we have a selected file from the form field
    if (widget.field.value != null) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(100),
        child: Image.file(
          widget.field.value!,
          fit: BoxFit.cover,
          width: 100,
          height: 100,
        ),
      );
    } else if (widget.imageUrl != null && widget.imageUrl!.isNotEmpty) {
      return CustomCachedNetworkImage(
        imageUrl: widget.imageUrl!,
        borderRadius: BorderRadius.circular(100),
        width: 100,
        height: 100,
        fit: BoxFit.cover,
      );
    } else {
      return CircleAvatar(
        backgroundColor: Theme.of(context).primaryColor,
        child: const Icon(
          Icons.person,
          color: Colors.white,
          size: 50,
        ),
      );
    }
  }

  void _showImagePickerBottomSheet() {
    Get.bottomSheet(
      CustomBottomSheet(
        title: 'تغيير الصورة الشخصية',
        body: Column(
          children: [
            ListTile(
              title: const Text('الكاميرا'),
              subtitle: const Text('التقاط صورة جديدة'),
              leading: const Icon(FeatherIcons.camera),
              onTap: () => _pickImage(ImageSource.camera),
            ),
            ListTile(
              title: const Text('المعرض'),
              subtitle: const Text('اختيار من الصور المحفوظة'),
              leading: const Icon(FeatherIcons.image),
              onTap: () => _pickImage(ImageSource.gallery),
            ),
            const Divider(),
            ListTile(
              title: const Text('معلومات'),
              subtitle: const Text('سيتم قص الصورة تلقائياً لتصبح مربعة الشكل'),
              leading: Icon(
                FeatherIcons.info,
                color: Theme.of(context).primaryColor,
              ),
              enabled: false,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _pickImage(ImageSource source) async {
    try {
      Get.back(); // Close bottom sheet

      final ImagePicker picker = ImagePicker();
      XFile? image;

      if (PlatformHelper.isWeb && source == ImageSource.camera) {
        // For web, use gallery instead of camera as camera might not be available
        image = await picker.pickImage(
          source: ImageSource.gallery,
          imageQuality: 85,
        );
      } else {
        image = await picker.pickImage(
          source: source,
          imageQuality: 85,
        );
      }

      if (image != null) {
        await _processAndUploadImage(image);
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء اختيار الصورة: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Future<void> _processAndUploadImage(XFile imageFile) async {
    _isUpdating.value = true;

    try {
      // Use ProfileController's cropping functionality
      final file = await _profileController.updateProfileImageWithCropping(imageFile);

      if (file != null) {
        // Update the form field value with the cropped image file
        widget.field.didChange(file);

        // Call the onChanged callback if provided
        widget.onChanged?.call(file);

        // Notify parent widget that image was updated
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحديث الصورة: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      _isUpdating.value = false;
    }
  }

  /// Method to manually trigger image update (can be called from parent)
  Future<void> updateImageFromFile(File imageFile) async {
    _isUpdating.value = true;

    try {
      final xFile = XFile(imageFile.path);
      final file = await _profileController.updateProfileImageWithCropping(xFile);
      if (file != null) {
        // Update the form field value with the cropped image file
        widget.field.didChange(file);

        // Call the onChanged callback if provided
        widget.onChanged?.call(file);

        // Show success message
        Get.snackbar(
          'تم بنجاح',
          'تم تحديث الصورة الشخصية بنجاح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحديث الصورة: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      _isUpdating.value = false;
    }
  }

  /// Get the currently selected image file from the form field
  File? get selectedImage => widget.field.value;

  /// Check if an update is currently in progress
  bool get isUpdating => _isUpdating.value;
}

import 'package:almashal/src/core/utils/common_functions.dart';
import 'package:almashal/src/core/values/assets.dart';
import 'package:almashal/src/core/values/colors.dart';
import 'package:almashal/src/custom_packages/marquee/marquee.dart';
import 'package:almashal/src/view/components/fabs/web_home_floating_action_button.dart';
import 'package:almashal/src/view/components/form_fields/search_form_field.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:share_plus/share_plus.dart';

class MasterPage extends StatefulWidget {
  final String title;
  final String? shareText;
  final String? shareLink;
  final Widget body;
  final Widget? beforeBackground;
  final PreferredSizeWidget? appBarBotton;
  final bool withBackground;
  final Widget? drawer;
  final Widget? floatingActionButton;
  final bool isSearchable;
  final String? searchType;
  final bool showSearchTextField;
  final List<Widget>? appBarActions;
  final Function(String?)? onSearchSubmitted;
  final Function(bool value)? onSearchToggled;
  const MasterPage({
    super.key,
    required this.title,
    required this.body,
    this.appBarBotton,
    this.beforeBackground,
    this.withBackground = false,
    this.drawer,
    this.floatingActionButton,
    this.shareText,
    this.shareLink,
    this.isSearchable = false,
    this.searchType,
    this.onSearchSubmitted,
    this.onSearchToggled,
    this.showSearchTextField = true,
    this.appBarActions,
  });

  @override
  State<MasterPage> createState() => _MasterPageState();
}

class _MasterPageState extends State<MasterPage> {
  bool searchEnabled = false;
  @override
  Widget build(BuildContext context) {
    Widget body = widget.body;
    if (searchEnabled) {
      body = widget.showSearchTextField
          ? Column(
              children: [
                SearchFormField(
                  onSearchSubmitted: widget.onSearchSubmitted,
                ),
                Expanded(child: widget.body),
              ],
            )
          : widget.body;
    }
    return Scaffold(
      drawer: widget.drawer,
      floatingActionButton: widget.floatingActionButton,
      appBar: widget.title.isEmpty
          ? null
          : AppBar(
              title: LayoutBuilder(builder: (context, constraints) {
                bool titleMoving = false;
                titleMoving = (CommonFunctions.getTextSize(
                          text: widget.title,
                          style: Get.theme.appBarTheme.titleTextStyle,
                        ).width -
                        16) >
                    constraints.maxWidth;
                return titleMoving
                    ? SizedBox(
                        height: kToolbarHeight,
                        child: Marquee(
                          text: widget.title,
                          scrollAxis: Axis.horizontal,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          blankSpace: 30,
                          velocity: 50,
                          startPadding: 20,
                          startAfter: const Duration(seconds: 2),
                          pauseAfterRound: const Duration(seconds: 1),
                          showFadingOnlyWhenScrolling: true,
                          fadingEdgeStartFraction: 0.1,
                          fadingEdgeEndFraction: 0.1,
                          accelerationDuration: const Duration(seconds: 1),
                          accelerationCurve: Curves.linear,
                          decelerationDuration:
                              const Duration(milliseconds: 500),
                          decelerationCurve: Curves.easeOut,
                          textDirection: TextDirection.ltr,
                        ),
                      )
                    : Text(widget.title);
              }),
              flexibleSpace: Container(
                decoration: const BoxDecoration(
                    image: DecorationImage(
                        image: AssetImage(Assets.appbarBg), fit: BoxFit.cover)),
              ),
              actions: <Widget>[
                    if (widget.shareText != null)
                      IconButton(
                        onPressed: () {
                          if (widget.shareText != null) {
                            var text = widget.shareText!;
                            if (widget.shareLink != null) {
                              text += "\n-------------\n${widget.shareLink}";
                            }
                            Share.share(text);
                          }
                        },
                        icon: const Icon(Icons.share),
                      ),
                    if (widget.isSearchable)
                      IconButton(
                        onPressed: () {
                          setState(() {
                            searchEnabled = !searchEnabled;
                            if (widget.onSearchToggled != null) {
                              widget.onSearchToggled!(searchEnabled);
                            }
                          });
                        },
                        icon: const Icon(Icons.search),
                      ),
                  ] +
                  (widget.appBarActions ?? []),
            ),
      body: Stack(
        children: [
          // المحتوى الأساسي
          SizedBox(
            height: double.maxFinite,
            width: double.maxFinite,
            child: !widget.withBackground
                ? body
                : Stack(
                    children: [
                      Positioned(
                        bottom: 0,
                        right: 0,
                        left: 0,
                        child: Column(
                          children: [
                            widget.beforeBackground ?? Container(),
                            Image.asset(
                              Assets.background,
                              fit: BoxFit.fitWidth,
                            ),
                          ],
                        ),
                      ),
                      Positioned(
                        bottom: 0,
                        right: 0,
                        left: 0,
                        top: 0,
                        child: Image.asset(
                          Assets.pattern,
                          fit: BoxFit.cover,
                        ),
                      ),
                      Positioned(
                        bottom: 0,
                        right: 0,
                        left: 0,
                        top: 0,
                        child: Container(
                          width: double.maxFinite,
                          height: double.maxFinite,
                          color:
                              AppColors.backgroundColor.withValues(alpha: 0.1),
                        ),
                      ),
                      body,
                    ],
                  ),
          ),
          // زر العودة للرئيسية للويب
          const WebHomeFloatingActionButton(),
        ],
      ),
    );
  }
}

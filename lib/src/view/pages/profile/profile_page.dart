import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide MultipartFile;
import 'package:intl/intl.dart';
import 'package:share_plus/share_plus.dart';
import 'package:image_picker/image_picker.dart';
import 'package:datetime_picker_formfield_new/datetime_picker_formfield.dart';
import 'package:feather_icons/feather_icons.dart';

import 'package:almashal/src/data/models/user.dart';
import 'package:almashal/src/data/models/vms/user_vm.dart';
import 'package:almashal/src/data/services/auth_service.dart';
import 'package:almashal/src/data/services/error_service.dart';
import 'package:almashal/src/data/services/network_service.dart';
import 'package:almashal/src/controllers/profile/achievements_controller.dart';
import 'package:almashal/src/controllers/profile/experiences_controller.dart';
import 'package:almashal/src/controllers/profile/skills_controller.dart';
import 'package:almashal/src/controllers/profile/cv_controller.dart';
import 'package:almashal/src/controllers/profile_controller.dart';
import 'package:almashal/src/view/components/buttons/custom_filled_button.dart';
import 'package:almashal/src/view/components/form_fields/app_text_form_field.dart';
import 'package:almashal/src/view/components/form_fields/change_avatar_form_field_with_cropping.dart';
import 'package:almashal/src/view/components/form_fields/custom_dropdwon_form_field.dart';
import 'package:almashal/src/view/components/layouts/master_page.dart';
import 'package:almashal/src/view/components/shared/loading_state.dart';
import 'package:almashal/src/core/routes/app_pages.dart';
import 'components/profile_header.dart';
import 'components/profile_tabs.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class ProfilePage extends StatefulWidget {
  final String? userId;

  const ProfilePage({super.key, this.userId});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  final RxBool _isLoading = true.obs;
  final RxBool _isOffline = false.obs;
  final Rx<User?> _profileUser = Rx<User?>(null);
  final RxBool _isEditing = false.obs;

  @override
  void initState() {
    super.initState();

    NetworkService.instance.addListener((connected) {
      _isOffline.value = !connected;
      if (connected && _profileUser.value == null) {
        _loadUserData();
      }
    });

    if (!Get.isRegistered<ExperiencesController>()) {
      Get.put(ExperiencesController());
    }
    if (!Get.isRegistered<AchievementsController>()) {
      Get.put(AchievementsController());
    }
    if (!Get.isRegistered<SkillsController>()) {
      Get.put(SkillsController());
    }
    if (!Get.isRegistered<CVController>()) {
      Get.put(CVController());
    }

    _loadUserData();
  }

  Future<void> _loadUserData() async {
    _isLoading.value = true;

    try {
      if (NetworkService.instance.connected.value) {
        if (widget.userId == null ||
            widget.userId!.isEmpty ||
            widget.userId == AuthService.instance.userData.value?.user.id.toString()) {
          _profileUser.value = AuthService.instance.userData.value?.user;
        } else {
          final user = await AuthService.instance.getPublicProfile(widget.userId!);
          _profileUser.value = user;
        }
      } else {
        _isOffline.value = true;
        if (widget.userId == null ||
            widget.userId!.isEmpty ||
            widget.userId == AuthService.instance.userData.value?.user.id.toString()) {
          _profileUser.value = AuthService.instance.userData.value?.user;
        } else {
          Get.snackbar(
            'تنبيه',
            'لا يمكن تحميل بيانات المستخدم بدون اتصال بالإنترنت',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.orange,
            colorText: Colors.white,
          );
        }
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحميل بيانات المستخدم',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  String _formatDate(DateTime? date) {
    if (date == null) return '';
    return DateFormat('yyyy/MM/dd').format(date);
  }

  @override
  Widget build(BuildContext context) {
    var formKey = GlobalKey<FormState>();
    UserVM data = UserVM();
    Map<String, dynamic> errors = {};

    return Obx(() {
      if (_isLoading.value) {
        return const Scaffold(
          body: LoadingState(
            message: 'جاري تحميل بيانات المستخدم...',
          ),
        );
      }

      if (_isOffline.value && _profileUser.value == null) {
        return Scaffold(
          appBar: AppBar(
            title: const Text('الملف الشخصي'),
            leading: IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => Get.back(),
            ),
          ),
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.signal_wifi_off,
                  size: 64,
                  color: Colors.grey,
                ),
                const SizedBox(height: 16),
                const Text(
                  'لا يوجد اتصال بالإنترنت',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى',
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: _loadUserData,
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          ),
        );
      }

      if (_profileUser.value == null) {
        return Scaffold(
          appBar: AppBar(
            title: const Text('الملف الشخصي'),
            leading: IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => Get.back(),
            ),
          ),
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.red,
                ),
                const SizedBox(height: 16),
                const Text(
                  'لا يمكن عرض الملف الشخصي',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'قد يكون المستخدم غير موجود أو لا تملك صلاحية الوصول',
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: () => Get.back(),
                  child: const Text('العودة'),
                ),
              ],
            ),
          ),
        );
      }

      final bool isOwner = AuthService.instance.userData.value?.user.id == _profileUser.value?.id;

      return MasterPage(
        title: _isEditing.value ? 'تعديل الملف الشخصي' : 'الملف الشخصي',
        appBarActions: [
          if (isOwner)
            IconButton(
              icon: Icon(_isEditing.value ? FeatherIcons.x : FeatherIcons.edit2),
              onPressed: () => _isEditing.value = !_isEditing.value,
            ),
          IconButton(
            icon: const Icon(Icons.family_restroom),
            onPressed: () {
              Get.toNamed(Routes.FAMILY_TREE_PAGE, arguments: {'initialTab': 1});
            },
            tooltip: 'العودة إلى الشجرة التفاعلية',
          ),
          IconButton(
            icon: const Icon(FeatherIcons.share2),
            onPressed: () {
              final String profileUrl = 'https://almashalfamily.com/profile/${_profileUser.value?.id}';
              Share.share(
                'تصفح الملف الشخصي لـ ${_profileUser.value?.firstName} ${_profileUser.value?.lastName} على تطبيق أسرة المشعل: $profileUrl',
                subject: 'مشاركة ملف شخصي من تطبيق أسرة المشعل',
              );
            },
          ),
        ],
        body: _isEditing.value && isOwner
            ? _buildEditForm(context, formKey, data, errors, _profileUser.value)
            : NestedScrollView(
                headerSliverBuilder: (context, innerBoxIsScrolled) {
                  return [
                    SliverToBoxAdapter(
                      child: ProfileHeader(
                        coverImageUrl: _profileUser.value?.coverImage ?? '',
                        profileImageUrl: _profileUser.value?.image ?? '',
                        name:
                            '${_profileUser.value?.firstName} ${_profileUser.value?.middleName} ${_profileUser.value?.lastName}',
                        title: _profileUser.value?.branch ?? '',
                        onEditProfile: isOwner ? (() => _isEditing.value = true) as VoidCallback : null,
                        onChangeCoverImage: isOwner ? _showCoverImagePicker : null,
                        isVisitor: !isOwner,
                      ),
                    ),
                  ];
                },
                body: ProfileTabs(
                  userId: _profileUser.value?.id.toString() ?? '',
                  isOwner: isOwner,
                  user: _profileUser.value,
                ),
              ),
      );
    });
  }

  Widget _buildEditForm(
      BuildContext context, GlobalKey<FormState> formKey, UserVM data, Map<String, dynamic> errors, User? user) {
    return Center(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Obx(
          () {
            if (!ErrorBag.instance.cleared.value) {
              errors = ErrorBag.instance.getErrors();
            }
            return Form(
              key: formKey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  ChangeAvatarFormFieldWithCropping(
                    imageUrl: user?.image ?? "",
                    onSaved: (value) {
                      if (value != null) {
                        data.image = File(value.path);
                      }
                    },
                    onChanged: (value) {
                      // Optional: Handle image selection if needed
                      if (value != null) {
                        data.image = File(value.path);
                      }
                    },
                  ),
                  const SizedBox(height: 16),
                  AppTextFormField(
                    labelText: 'الاسم الاول',
                    errorText: errors['first_name']?[0],
                    keyboardType: TextInputType.name,
                    initialValue: user?.firstName,
                    validator: (value) {
                      if (value != null && value.isEmpty) {
                        return 'هذا الحقل مطلوب';
                      }
                      return null;
                    },
                    onSaved: (value) {
                      if (value != null) data.firstName = value;
                    },
                  ),

                  AppTextFormField(
                    margin: const EdgeInsets.only(top: 16),
                    labelText: 'اسم الاب',
                    errorText: errors['middle_name']?[0],
                    keyboardType: TextInputType.name,
                    initialValue: user?.middleName,
                    validator: (value) {
                      if (value != null && value.isEmpty) {
                        return 'هذا الحقل مطلوب';
                      }
                      return null;
                    },
                    onSaved: (value) {
                      if (value != null) data.middleName = value;
                    },
                  ),
                  AppTextFormField(
                    margin: const EdgeInsets.only(top: 16),
                    labelText: 'الاسم الاخير',
                    errorText: errors['last_name']?[0],
                    keyboardType: TextInputType.name,
                    initialValue: user?.lastName,
                    validator: (value) {
                      if (value != null && value.isEmpty) {
                        return 'هذا الحقل مطلوب';
                      }
                      return null;
                    },
                    onSaved: (value) {
                      if (value != null) data.lastName = value;
                    },
                  ),
                  AppTextFormField(
                    margin: const EdgeInsets.only(top: 16),
                    labelText: 'نبذة عني',
                    errorText: errors['overview']?[0],
                    keyboardType: TextInputType.multiline,
                    maxLines: 4,
                    initialValue: user?.overview,
                    hintText: 'اكتب نبذة مختصرة عن نفسك',
                    onSaved: (value) {
                      if (value != null) data.overview = value;
                    },
                  ),
                  AppTextFormField(
                    margin: const EdgeInsets.only(top: 16),
                    labelText: 'البريد الالكتروني',
                    errorText: errors['email']?[0],
                    keyboardType: TextInputType.emailAddress,
                    initialValue: user?.email,
                    validator: (value) {
                      if (value != null && value.isNotEmpty) {
                        if (!GetUtils.isEmail(value)) {
                          return 'البريد الالكتروني غير صحيح';
                        }
                      }
                      return null;
                    },
                    onSaved: (value) {
                      if (value != null) data.email = value;
                    },
                  ),
                  AppTextFormField(
                    margin: const EdgeInsets.only(top: 16),
                    labelText: 'رقم الجوال',
                    errorText: errors['mobile']?[0],
                    keyboardType: TextInputType.phone,
                    initialValue: user?.mobile,
                    validator: (value) {
                      if (value != null && value.isNotEmpty) {
                        // validation for mobile
                      }
                      return null;
                    },
                    onSaved: (value) {
                      if (value != null) data.mobile = value;
                    },
                  ),
                  SizedBox(
                    height: 16,
                  ),
                  DateTimeField(
                    decoration: const InputDecoration(
                      labelText: 'تاريخ الميلاد',
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 16,
                      ),
                      border: OutlineInputBorder(),
                    ),
                    initialValue: user?.birthDate,
                    format: DateFormat('yyyy/MM/dd'),
                    onShowPicker: (context, currentValue) async {
                      return await showDatePicker(
                        context: context,
                        firstDate: DateTime(1900),
                        initialDate: currentValue ?? DateTime.now(),
                        lastDate: DateTime.now(),
                      );
                    },
                    onSaved: (value) {
                      if (value != null) data.birthDate = value;
                    },
                  ),
                  AppTextFormField(
                    margin: const EdgeInsets.only(top: 16),
                    labelText: 'مكان الميلاد',
                    errorText: errors['birth_place']?[0],
                    keyboardType: TextInputType.text,
                    initialValue: user?.birthPlace,
                    validator: (value) {
                      return null;
                    },
                    onSaved: (value) {
                      if (value != null) data.birthPlace = value;
                    },
                  ),
                  Obx(() => CustomDropdownFormField<int?>(
                        margin: const EdgeInsets.only(top: 16),
                        label: const Text('الفرع في الشجرة'),
                        errorText: errors['branch_id']?[0],
                        value: user?.branchId,
                        items: AuthService.instance.branches.value
                            .map(
                              (data) => DropdownMenuItem(
                                value: data.id,
                                child: Text(data.name),
                              ),
                            )
                            .toList(),
                        validator: (value) {
                          if (value == null) {
                            return 'هذا الحقل مطلوب';
                          }

                          return null;
                        },
                        onChanged: (value) {
                          if (value != null) data.branchId = value;
                        },
                        onSave: (value) {
                          if (value != null) data.branchId = value;
                        },
                      )),
                  AppTextFormField(
                    margin: const EdgeInsets.only(top: 16),
                    labelText: 'العنوان',
                    errorText: errors['address']?[0],
                    keyboardType: TextInputType.text,
                    initialValue: user?.address,
                    validator: (value) {
                      return null;
                    },
                    onSaved: (value) {
                      if (value != null) data.address = value;
                    },
                  ),

                  // قسم روابط التواصل الاجتماعي المحدث
                  const SizedBox(height: 24),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surfaceVariant,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Theme.of(context).dividerColor.withOpacity(0.1),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.link, color: Theme.of(context).primaryColor),
                            const SizedBox(width: 8),
                            Text(
                              'روابط التواصل الاجتماعي',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        _buildSocialLinkField(
                          context: context,
                          icon: FaIcon(FontAwesomeIcons.facebookF, color: Colors.blue[700]),
                          label: 'فيسبوك',
                          errorText: errors['facebook_link']?[0],
                          initialValue: user?.facebookLink ?? '',
                          hintText: 'https://facebook.com/username',
                          onSaved: (value) {
                            if (value != null) data.facebookLink = value;
                          },
                        ),
                        _buildSocialLinkField(
                          context: context,
                          icon: FaIcon(FontAwesomeIcons.xTwitter, color: Colors.black),
                          label: 'إكس (تويتر)',
                          errorText: errors['x_link']?[0],
                          initialValue: user?.xLink ?? '',
                          hintText: 'https://x.com/username',
                          onSaved: (value) {
                            if (value != null) data.xLink = value;
                          },
                        ),
                        _buildSocialLinkField(
                          context: context,
                          icon: FaIcon(FontAwesomeIcons.snapchat, color: Colors.yellow[700]),
                          label: 'سناب شات',
                          errorText: errors['snapshot_link']?[0],
                          initialValue: user?.snapshotLink ?? '',
                          hintText: 'https://snapchat.com/add/username',
                          onSaved: (value) {
                            if (value != null) data.snapshotLink = value;
                          },
                        ),
                        _buildSocialLinkField(
                          context: context,
                          icon: FaIcon(FontAwesomeIcons.youtube, color: Colors.red[700]),
                          label: 'يوتيوب',
                          errorText: errors['youtube_link']?[0],
                          initialValue: user?.youtubeLink ?? '',
                          hintText: 'https://youtube.com/c/username',
                          onSaved: (value) {
                            if (value != null) data.youtubeLink = value;
                          },
                        ),
                        _buildSocialLinkField(
                          context: context,
                          icon: FaIcon(FontAwesomeIcons.linkedinIn, color: Colors.blue[800]),
                          label: 'لينكد إن',
                          errorText: errors['linkedin_link']?[0],
                          initialValue: user?.linkedinLink ?? '',
                          hintText: 'https://linkedin.com/in/username',
                          onSaved: (value) {
                            if (value != null) data.linkedinLink = value;
                          },
                        ),
                        _buildSocialLinkField(
                          context: context,
                          icon: FaIcon(FontAwesomeIcons.instagram, color: Colors.pink),
                          label: 'انستغرام',
                          errorText: errors['instagram_link']?[0],
                          initialValue: user?.instagramLink ?? '',
                          hintText: 'https://instagram.com/username',
                          onSaved: (value) {
                            if (value != null) data.instagramLink = value;
                          },
                        ),
                        _buildSocialLinkField(
                          context: context,
                          icon: FaIcon(FontAwesomeIcons.tiktok, color: Colors.black),
                          label: 'تيك توك',
                          errorText: errors['tiktok_link']?[0],
                          initialValue: user?.tiktokLink ?? '',
                          hintText: 'https://tiktok.com/@username',
                          onSaved: (value) {
                            if (value != null) data.tiktokLink = value;
                          },
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 32),
                  SizedBox(
                    width: double.infinity,
                    child: CustomFilledButton(
                      label: 'تعديل',
                      onTap: () async {
                        if (formKey.currentState?.validate() ?? false) {
                          formKey.currentState?.save();

                          try {
                            await AuthService.instance.updateProfile(data, data.image);

                            _loadUserData();
                            _isEditing.value = false;

                            Get.snackbar(
                              'تم',
                              'تم تحديث الملف الشخصي بنجاح',
                              snackPosition: SnackPosition.BOTTOM,
                              backgroundColor: Colors.green,
                              colorText: Colors.white,
                            );
                          } catch (e) {
                            // Get.back();
                            Get.snackbar(
                              'خطأ',
                              'حدث خطأ أثناء تحديث الملف الشخصي',
                              snackPosition: SnackPosition.BOTTOM,
                              backgroundColor: Colors.red,
                              colorText: Colors.white,
                            );
                          }
                        }
                      },
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildSocialLinkField({
    required BuildContext context,
    required Widget icon,
    required String label,
    required String? errorText,
    required String initialValue,
    required String hintText,
    required FormFieldSetter<String> onSaved,
  }) {
    final controller = TextEditingController(text: initialValue);

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: TextFormField(
        controller: controller,
        decoration: InputDecoration(
          labelText: label,
          hintText: hintText,
          errorText: errorText,
          prefixIcon: Padding(
            padding: const EdgeInsets.all(12),
            child: icon,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          contentPadding: const EdgeInsets.symmetric(
            vertical: 12,
            horizontal: 16,
          ),
        ),
        keyboardType: TextInputType.url,
        onSaved: onSaved,
        validator: (value) {
          if (value != null && value.isNotEmpty) {
            if (!value.startsWith('http') && !value.startsWith('https')) {
              return 'يجب أن يبدأ الرابط بـ http:// أو https://';
            }
          }
          return null;
        },
      ),
    );
  }

  void _showCoverImagePicker() async {
    final result = await showModalBottomSheet<String>(
      context: context,
      builder: (context) => SafeArea(
        child: Wrap(
          children: [
            ListTile(
              leading: const Icon(FeatherIcons.camera),
              title: const Text('التقاط صورة من الكاميرا'),
              onTap: () => Navigator.of(context).pop('camera'),
            ),
            ListTile(
              leading: const Icon(FeatherIcons.image),
              title: const Text('اختيار من المعرض'),
              onTap: () => Navigator.of(context).pop('gallery'),
            ),
            const Divider(),
            ListTile(
              leading: Icon(
                FeatherIcons.info,
                color: Theme.of(context).primaryColor,
              ),
              title: const Text('معلومات'),
              subtitle: const Text('يمكنك اختيار قص الصورة لتصبح مربعة أو الاحتفاظ بالحجم الأصلي'),
              enabled: false,
            ),
          ],
        ),
      ),
    );

    if (result == null) return;

    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(
      source: result == 'camera' ? ImageSource.camera : ImageSource.gallery,
      imageQuality: 85,
    );

    if (pickedFile == null) return;

    _updateCoverImageWithCropping(pickedFile);
  }

  void _updateCoverImageWithCropping(XFile imageFile) async {
    try {
      final profileController = Get.find<ProfileController>();

      // Show options for cover image: crop to square or keep original
      final shouldCrop = await Get.dialog<bool>(
        AlertDialog(
          title: const Text('صورة الغلاف'),
          content: const Text('هل تريد قص الصورة لتصبح مربعة أم الاحتفاظ بالحجم الأصلي؟'),
          actions: [
            TextButton(
              onPressed: () => Get.back(result: false),
              child: const Text('الحجم الأصلي'),
            ),
            ElevatedButton(
              onPressed: () => Get.back(result: true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('قص مربع'),
            ),
          ],
        ),
      );

      if (shouldCrop == null) return; // User cancelled

      final success = await profileController.updateCoverImageWithCropping(
        imageFile,
        forceSquare: shouldCrop,
      );

      if (success) {
        _loadUserData(); // Reload to show updated cover image
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحديث صورة الغلاف: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
}

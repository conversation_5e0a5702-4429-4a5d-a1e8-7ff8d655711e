import 'package:flutter/material.dart';
import 'package:almashal/src/view/pages/profile/components/tabs/cv_tab.dart';
import 'package:almashal/src/view/pages/profile/components/tabs/skills_tab.dart';
import 'package:almashal/src/data/models/user.dart';
import 'package:almashal/src/data/services/auth_service.dart';
import 'package:almashal/src/controllers/profile/experiences_controller.dart';
import 'package:almashal/src/controllers/profile/achievements_controller.dart';
import 'package:almashal/src/controllers/profile/skills_controller.dart';
import 'package:almashal/src/controllers/profile/cv_controller.dart';
import 'tabs/basic_info_tab.dart';
import 'tabs/achievements_tab.dart';
import 'tabs/reviews_tab.dart';
import 'tabs/experience_tab.dart';
import 'package:feather_icons/feather_icons.dart';
import 'package:get/get.dart';

class ProfileTabs extends StatefulWidget {
  final bool isOwner;
  final String userId;
  final User? user;

  const ProfileTabs({
    super.key,
    required this.userId,
    this.isOwner = false,
    this.user,
  });

  @override
  State<ProfileTabs> createState() => _ProfileTabsState();
}

class _ProfileTabsState extends State<ProfileTabs>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  User? user;
  bool _showTooltip = true;
  final GlobalKey _tabBarKey = GlobalKey();

  // تعريف متحكمات التبويبات
  late ExperiencesController _experiencesController;
  late AchievementsController _achievementsController;
  late SkillsController _skillsController;
  // late CVController _cvController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: widget.isOwner ? 5 : 5,
      vsync: this,
    );
    _loadUserData();

    // تهيئة المتحكمات وتعيين معرف المستخدم
    _initControllers();

    // إظهار التلميح بعد بناء الواجهة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _showTabsTooltip();
    });
  }

  // تهيئة المتحكمات وتعيين معرف المستخدم
  void _initControllers() {
    // الحصول على وحدات التحكم
    _experiencesController = Get.find<ExperiencesController>();
    _achievementsController = Get.find<AchievementsController>();
    _skillsController = Get.find<SkillsController>();
    // _cvController = Get.find<CVController>();

    // تعيين معرف المستخدم لكل وحدة تحكم
    _experiencesController.setUserId(widget.userId);
    _achievementsController.setUserId(widget.userId);
    _skillsController.setUserId(widget.userId);
    // _cvController.setUserId(widget.userId);
  }

  void _showTabsTooltip() {
    if (_showTooltip) {
      showDialog(
        context: context,
        barrierDismissible: false,
        barrierColor: Colors.black54,
        builder: (context) => AlertDialog(
          backgroundColor: Colors.transparent,
          elevation: 0,
          content: TweenAnimationBuilder<double>(
            tween: Tween(begin: 0.0, end: 1.0),
            duration: const Duration(milliseconds: 500),
            builder: (context, value, child) {
              return Opacity(
                opacity: value,
                child: child,
              );
            },
            child: Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    spreadRadius: 5,
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Theme.of(context)
                          .colorScheme
                          .primaryContainer
                          .withOpacity(0.3),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.swipe,
                      size: 40,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'اسحب يميناً أو يساراً',
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'للتنقل بين التبويبات',
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Theme.of(context).colorScheme.secondary,
                        ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      // إغلاق التلميح بعد 3 ثواني
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted) {
          Navigator.of(context).pop();
          setState(() => _showTooltip = false);
        }
      });
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadUserData() async {
    setState(() {
      if (widget.user == null) {
        user = AuthService.instance.userData.value?.user;
      } else {
        user = widget.user;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // استخدام Scaffold داخل التبويبات لمعالجة مشكلة لوحة المفاتيح
    return Scaffold(
      // استخدام resizeToAvoidBottomInset لمنع تداخل لوحة المفاتيح مع المحتوى
      resizeToAvoidBottomInset: true,
      body: Column(
        children: [
          // شريط التبويبات
          Container(
            key: _tabBarKey,
            margin: const EdgeInsets.symmetric(horizontal: 8),
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(15),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: TabBar(
              controller: _tabController,
              labelColor: Colors.brown,
              unselectedLabelColor: Colors.grey,
              indicatorColor: Colors.brown,
              indicatorSize: TabBarIndicatorSize.label,
              isScrollable: true,
              tabAlignment: TabAlignment.start,
              tabs: widget.isOwner
                  ? const [
                      Tab(
                          icon: Icon(FeatherIcons.user),
                          text: 'المعلومات الأساسية'),
                      Tab(
                          icon: Icon(FeatherIcons.fileText),
                          text: 'السيرة الذاتية'),
                      Tab(icon: Icon(FeatherIcons.briefcase), text: 'الخبرات'),
                      Tab(icon: Icon(FeatherIcons.award), text: 'المهارات'),
                      // Tab(icon: Icon(FeatherIcons.star), text: 'الإنجازات'),
                      Tab(icon: Icon(FeatherIcons.thumbsUp), text: 'التقييمات'),
                      //Tab(icon: Icon(FeatherIcons.image), text: 'المعرض'),
                    ]
                  : const [
                      Tab(
                          icon: Icon(FeatherIcons.user),
                          text: 'المعلومات الأساسية'),
                      Tab(icon: Icon(FeatherIcons.briefcase), text: 'الخبرات'),
                      Tab(icon: Icon(FeatherIcons.award), text: 'المهارات'),
                      // Tab(icon: Icon(FeatherIcons.star), text: 'الإنجازات'),
                      //  Tab(icon: Icon(FeatherIcons.image), text: 'المعرض'),
                      Tab(icon: Icon(FeatherIcons.thumbsUp), text: 'التقييمات'),
                    ],
            ),
          ),
          // محتوى التبويبات
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: widget.isOwner
                  ? [
                      BasicInfoTab(isOwner: widget.isOwner, user: user),
                      CVTab(
                        isOwner: widget.isOwner,
                        userId: widget.userId,
                        user: widget.user,
                      ),
                      ExperienceTab(
                          isOwner: widget.isOwner, userId: widget.userId),
                      SkillsTab(isOwner: widget.isOwner, userId: widget.userId),
                      // AchievementsTab(
                      //     isOwner: widget.isOwner, userId: widget.userId),
                      ReviewsTab(userId: widget.userId),
                      //  GalleryTab(isOwner: widget.isOwner),
                    ]
                  : [
                      BasicInfoTab(isOwner: widget.isOwner, user: user),
                      ExperienceTab(
                          isOwner: widget.isOwner, userId: widget.userId),
                      SkillsTab(isOwner: widget.isOwner, userId: widget.userId),
                      // AchievementsTab(
                      //     isOwner: widget.isOwner, userId: widget.userId),
                      //GalleryTab(isOwner: widget.isOwner),
                      ReviewsTab(userId: widget.userId),
                    ],
            ),
          ),
        ],
      ),
    );
  }
}

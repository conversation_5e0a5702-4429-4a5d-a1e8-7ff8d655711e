import 'package:almashal/src/controllers/profile/reviews_controller.dart';
import 'package:almashal/src/data/models/profile/review.dart';
import 'package:almashal/src/view/components/image/custom_cached_network_image.dart';
import 'package:almashal/src/view/components/shared/index.dart';
import 'package:almashal/src/data/services/auth_service.dart';
import 'package:almashal/src/core/theme/app_colors.dart';
import 'package:almashal/src/core/theme/app_dimensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';

class ReviewsTab extends StatefulWidget {
  const ReviewsTab({
    super.key,
    required this.userId,
  });

  final String userId;

  @override
  State<ReviewsTab> createState() => _ReviewsTabState();
}

class _ReviewsTabState extends State<ReviewsTab> {
  late final ReviewsController _controller;
  final _formKey = GlobalKey<FormState>();
  final _commentController = TextEditingController();
  double _rating = 3.0;

  @override
  void initState() {
    super.initState();
    // استخدام Put.lazyPut لتسجيل وحدة التحكم بشكل كسول
    _controller = Get.put(ReviewsController());
    _controller.setUserId(widget.userId);
  }

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: _controller.loadReviews,
        child: Obx(() {
          if (_controller.isLoading.value) {
            return const LoadingState(
              message: 'جاري تحميل التقييمات...',
              type: LoadingIndicatorType.threeBounce,
            );
          }

          if (_controller.reviewsList.isEmpty) {
            return EmptyState(
              message: 'لا توجد تقييمات',
              description: 'لم يقم أحد بتقييم هذا المستخدم بعد',
              icon: Icons.rate_review,
              onRefresh: _controller.loadReviews,
            );
          }

          return Column(
            children: [
              // ملخص المراجعات
              _buildReviewSummary(),

              // قائمة المراجعات
              Expanded(
                child: ListView.builder(
                  padding: EdgeInsets.all(AppDimensions.medium),
                  itemCount: _controller.reviewsList.length + (_controller.canLoadMore() ? 1 : 0),
                  itemBuilder: (context, index) {
                    // التحقق من إمكانية تحميل المزيد من البيانات
                    if (index == _controller.reviewsList.length) {
                      _controller.loadMoreReviews();
                      return Padding(
                        padding: EdgeInsets.symmetric(vertical: AppDimensions.medium),
                        child: Center(
                          child: SpinKitThreeBounce(
                            color: AppColors.primary,
                            size: 20,
                          ),
                        ),
                      );
                    }
                    return _buildReviewCard(_controller.reviewsList[index]);
                  },
                ),
              ),
            ],
          );
        }),
      ),
      // زر إضافة مراجعة (فقط للمستخدمين المسجلين ومختلفين عن صاحب الملف)
      floatingActionButton: AuthService.instance.isAuthenticated() &&
              AuthService.instance.userData.value!.user.id.toString() != widget.userId
          ? FloatingActionButton(
              onPressed: () => _showAddReviewDialog(),
              tooltip: 'إضافة تقييم',
              backgroundColor: AppColors.primary,
              child: const Icon(Icons.rate_review),
            )
          : null,
    );
  }

  Widget _buildReviewSummary() {
    return Obx(() {
      if (_controller.isSummaryLoading.value) {
        return Container(
          padding: EdgeInsets.all(AppDimensions.medium),
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        );
      }

      final summary = _controller.reviewSummary.value;
      if (summary == null) return const SizedBox.shrink();

      return Container(
        margin: EdgeInsets.all(AppDimensions.medium),
        padding: EdgeInsets.all(AppDimensions.large),
        decoration: BoxDecoration(
          color: AppColors.surfaceLight,
          borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
          border: Border.all(color: AppColors.primary.withValues(alpha: 0.2)),
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildSummaryItem(
                  'إجمالي التقييمات',
                  summary.reviewsCount.toString(),
                  Icons.rate_review,
                ),
                _buildSummaryItem(
                  'متوسط التقييم',
                  summary.averageRating.toStringAsFixed(1),
                  Icons.star,
                ),
              ],
            ),
            if (summary.ratingsBreakdown.isNotEmpty) ...[
              SizedBox(height: AppDimensions.medium),
              const Divider(),
              SizedBox(height: AppDimensions.medium),
              _buildRatingsBreakdown(summary.ratingsBreakdown),
            ],
          ],
        ),
      );
    });
  }

  Widget _buildSummaryItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          color: AppColors.primary,
          size: 32,
        ),
        SizedBox(height: AppDimensions.small),
        Text(
          value,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: AppColors.primary,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildRatingsBreakdown(Map<String, int> breakdown) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'توزيع التقييمات',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        SizedBox(height: AppDimensions.small),
        ...breakdown.entries.map((entry) {
          final stars = int.tryParse(entry.key) ?? 0;
          final count = entry.value;
          return Padding(
            padding: EdgeInsets.symmetric(vertical: 2),
            child: Row(
              children: [
                Row(
                  children: List.generate(
                    stars,
                    (index) => Icon(
                      Icons.star,
                      color: Colors.amber,
                      size: 16,
                    ),
                  ),
                ),
                SizedBox(width: AppDimensions.small),
                Text('($count)'),
              ],
            ),
          );
        }),
      ],
    );
  }

  Widget _buildReviewCard(Review review) {
    final currentUserId = AuthService.instance.userData.value?.user.id;
    final canEditDelete = currentUserId != null && review.isReviewerCurrentUser(currentUserId);

    return CustomCard(
      margin: EdgeInsets.only(bottom: AppDimensions.medium),
      title: review.reviewer.displayName,
      subtitle: review.createdAtFormatted.isNotEmpty ? review.createdAtFormatted : review.createdAt,
      titleIcon: Icons.person,
      actions: canEditDelete
          ? [
              PopupMenuButton<String>(
                onSelected: (value) {
                  if (value == 'edit') {
                    _showEditReviewDialog(review);
                  } else if (value == 'delete') {
                    _showDeleteConfirmation(review);
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit, size: 16),
                        SizedBox(width: 8),
                        Text('تعديل'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, size: 16, color: Colors.red),
                        SizedBox(width: 8),
                        Text('حذف', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
              ),
            ]
          : null,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // صورة المستخدم والتقييم بالنجوم
          Row(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(AppDimensions.borderRadiusCircular),
                child: CustomCachedNetworkImage(
                  imageUrl: review.reviewer.image ?? "",
                  width: AppDimensions.avatarSizeMedium,
                  height: AppDimensions.avatarSizeMedium,
                ),
              ),
              SizedBox(width: AppDimensions.medium),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: List.generate(
                        5,
                        (starIndex) => Icon(
                          starIndex < review.rating ? Icons.star : Icons.star_border,
                          color: Colors.amber,
                          size: 16,
                        ),
                      ),
                    ),
                    if (review.status == false)
                      Container(
                        margin: EdgeInsets.only(top: 4),
                        padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.orange.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          'في انتظار المراجعة',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.orange,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: AppDimensions.medium),
          // نص التقييم
          if (review.comment.isNotEmpty)
            Text(
              review.comment,
              style: TextStyle(
                color: AppColors.textPrimary,
                fontSize: AppDimensions.fontSizeRegular,
              ),
            ),
        ],
      ),
    );
  }

  // نافذة إضافة مراجعة جديدة
  Future<void> _showAddReviewDialog() async {
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.borderRadiusLarge),
        ),
        child: SingleChildScrollView(
          padding: EdgeInsets.all(AppDimensions.large),
          child: Form(
            key: _formKey,
            autovalidateMode: AutovalidateMode.onUserInteraction,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'إضافة تقييم',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                ),
                SizedBox(height: AppDimensions.large),

                // مكون التقييم بالنجوم
                Directionality(
                  textDirection: TextDirection.ltr,
                  child: Slider(
                    value: _rating,
                    onChanged: (value) {
                      setState(() {
                        _rating = value;
                      });
                    },
                    min: 1,
                    max: 5,
                    divisions: 4,
                    label: _rating.round().toString(),
                    activeColor: AppColors.primary,
                  ),
                ),

                // عرض النجوم المرئية
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(5, (index) {
                    return IconButton(
                      icon: Icon(
                        index < _rating.round() ? Icons.star : Icons.star_border,
                        color: Colors.amber,
                      ),
                      onPressed: () {
                        setState(() {
                          _rating = (index + 1).toDouble();
                        });
                      },
                    );
                  }),
                ),

                SizedBox(height: AppDimensions.medium),

                // حقل نص التقييم
                TextFormField(
                  controller: _commentController,
                  maxLines: 3,
                  decoration: InputDecoration(
                    labelText: 'نص التقييم',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
                    ),
                    filled: true,
                    fillColor: AppColors.surfaceLight,
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
                      borderSide: BorderSide(color: AppColors.primary.withValues(alpha: 0.5)),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
                      borderSide: BorderSide(color: AppColors.primary, width: 2),
                    ),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'الرجاء إدخال نص التقييم';
                    }
                    return null;
                  },
                ),

                SizedBox(height: AppDimensions.large),

                // أزرار الإلغاء والحفظ
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      style: TextButton.styleFrom(
                        foregroundColor: AppColors.textSecondary,
                        padding: EdgeInsets.symmetric(
                          horizontal: AppDimensions.medium,
                          vertical: AppDimensions.small,
                        ),
                      ),
                      child: const Text('إلغاء'),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        if (_formKey.currentState?.validate() ?? false) {
                          // إرسال التقييم
                          _controller.addReview({
                            'user_id': widget.userId,
                            'rating': _rating.round(),
                            'comment': _commentController.text,
                          });
                          Navigator.of(context).pop();
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                        padding: EdgeInsets.symmetric(
                          horizontal: AppDimensions.medium,
                          vertical: AppDimensions.small,
                        ),
                      ),
                      child: const Text('حفظ التقييم'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // نافذة تعديل مراجعة موجودة
  Future<void> _showEditReviewDialog(Review review) async {
    _rating = review.rating.toDouble();
    _commentController.text = review.comment;

    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.borderRadiusLarge),
        ),
        child: SingleChildScrollView(
          padding: EdgeInsets.all(AppDimensions.large),
          child: Form(
            key: _formKey,
            autovalidateMode: AutovalidateMode.onUserInteraction,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'تعديل التقييم',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                ),
                SizedBox(height: AppDimensions.large),

                // مكون التقييم بالنجوم
                Directionality(
                  textDirection: TextDirection.ltr,
                  child: Slider(
                    value: _rating,
                    onChanged: (value) {
                      setState(() {
                        _rating = value;
                      });
                    },
                    min: 1,
                    max: 5,
                    divisions: 4,
                    label: _rating.round().toString(),
                    activeColor: AppColors.primary,
                  ),
                ),

                // عرض النجوم المرئية
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(5, (index) {
                    return IconButton(
                      icon: Icon(
                        index < _rating.round() ? Icons.star : Icons.star_border,
                        color: Colors.amber,
                      ),
                      onPressed: () {
                        setState(() {
                          _rating = (index + 1).toDouble();
                        });
                      },
                    );
                  }),
                ),

                SizedBox(height: AppDimensions.medium),

                // حقل نص التقييم
                TextFormField(
                  controller: _commentController,
                  maxLines: 3,
                  decoration: InputDecoration(
                    labelText: 'نص التقييم',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
                    ),
                    filled: true,
                    fillColor: AppColors.surfaceLight,
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
                      borderSide: BorderSide(color: AppColors.primary.withValues(alpha: 0.5)),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
                      borderSide: BorderSide(color: AppColors.primary, width: 2),
                    ),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'الرجاء إدخال نص التقييم';
                    }
                    return null;
                  },
                ),

                SizedBox(height: AppDimensions.large),

                // أزرار الإلغاء والحفظ
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      style: TextButton.styleFrom(
                        foregroundColor: AppColors.textSecondary,
                        padding: EdgeInsets.symmetric(
                          horizontal: AppDimensions.medium,
                          vertical: AppDimensions.small,
                        ),
                      ),
                      child: const Text('إلغاء'),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        if (_formKey.currentState?.validate() ?? false) {
                          // تحديث التقييم
                          _controller.updateReview(review.id, {
                            'rating': _rating.round(),
                            'comment': _commentController.text,
                          });
                          Navigator.of(context).pop();
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                        padding: EdgeInsets.symmetric(
                          horizontal: AppDimensions.medium,
                          vertical: AppDimensions.small,
                        ),
                      ),
                      child: const Text('حفظ التعديل'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // تأكيد حذف المراجعة
  Future<void> _showDeleteConfirmation(Review review) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف هذا التقييم؟ لا يمكن التراجع عن هذا الإجراء.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      _controller.deleteReview(review.id);
    }
  }
}

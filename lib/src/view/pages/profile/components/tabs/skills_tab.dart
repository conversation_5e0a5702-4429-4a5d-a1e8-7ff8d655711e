import 'package:almashal/src/view/pages/profile/widget/empty_state.dart';
import 'package:almashal/src/view/pages/profile/widget/loading_indicator.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:almashal/src/controllers/profile/skills_controller.dart';
import 'package:almashal/src/data/models/profile/skill.dart';

class SkillsTab extends StatefulWidget {
  final bool isOwner;
  final String userId;

  const SkillsTab({
    super.key,
    required this.isOwner,
    required this.userId,
  });

  @override
  State<SkillsTab> createState() => _SkillsTabState();
}

class _SkillsTabState extends State<SkillsTab> {
  late final SkillsController _controller;
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  int _selectedLevel = 1;

  @override
  void initState() {
    super.initState();
    _controller = Get.find<SkillsController>();
    _controller.setUserId(widget.userId);
    // _controller.loadSkills();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _showAddEditDialog([Skill? skill]) async {
    if (skill != null) {
      _titleController.text = skill.name;
      _descriptionController.text = skill.description;
      _selectedLevel = skill.level;
    } else {
      _titleController.clear();
      _descriptionController.clear();
      _selectedLevel = 0;
    }

    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            autovalidateMode: AutovalidateMode.onUserInteraction,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  skill != null ? 'تعديل مهارة' : 'إضافة مهارة',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                ),
                const SizedBox(height: 24),
                TextFormField(
                  controller: _titleController,
                  decoration: InputDecoration(
                    labelText: 'اسم المهارة',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    prefixIcon: const Icon(Icons.title),
                    filled: true,
                    fillColor:
                        Theme.of(context).colorScheme.surfaceContainerHighest,
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'الرجاء إدخال اسم المهارة';
                    }
                    if (value.length < 2) {
                      return 'اسم المهارة قصير جداً';
                    }
                    if (value.length > 50) {
                      return 'اسم المهارة طويل جداً';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _descriptionController,
                  maxLines: 3,
                  decoration: InputDecoration(
                    labelText: 'وصف المهارة',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    prefixIcon: const Icon(Icons.description),
                    filled: true,
                    fillColor:
                        Theme.of(context).colorScheme.surfaceContainerHighest,
                  ),
                  validator: (value) {
                    if (value != null && value.length > 500) {
                      return 'الوصف طويل جداً';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                LevelFormField(
                  initialValue: _selectedLevel,
                  onChanged: (value) {
                    _selectedLevel = value;
                  },
                ),
                const SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      style: TextButton.styleFrom(
                        foregroundColor: Theme.of(context).colorScheme.error,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 24, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('إلغاء'),
                    ),
                    ElevatedButton(
                      onPressed: () async {
                        if (_formKey.currentState!.validate()) {
                          Get.back();
                          skill != null
                              ? await _controller.updateSkill(
                                  skill.id.toString(),
                                  _titleController.text,
                                  _descriptionController.text,
                                  _selectedLevel,
                                )
                              : await _controller.addSkill(
                                  _titleController.text,
                                  _descriptionController.text,
                                  _selectedLevel,
                                );
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        foregroundColor:
                            Theme.of(context).colorScheme.onPrimary,
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 24, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(skill != null ? 'تحديث' : 'إضافة'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _confirmDelete(Skill skill) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف مهارة "${skill.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _controller.deleteSkill(skill.id.toString());
    }
  }

  Widget _buildSkillLevel(int level) {
    return LinearProgressIndicator(
      backgroundColor: Colors.grey.shade300,
      value: level / 100,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: _controller.loadSkills,
        child: Obx(() {
          if (_controller.isLoading.value) {
            return const LoadingIndicator();
          }

          if (_controller.skillsList.isEmpty) {
            return EmptyState(
              message: 'لا توجد مهارات مضافة',
              onRefresh: _controller.loadSkills,
            );
          }

          return ListView.separated(
            padding: const EdgeInsets.all(16),
            itemCount: _controller.skillsList.length,
            separatorBuilder: (context, index) => const Divider(),
            itemBuilder: (context, index) {
              final skill = _controller.skillsList[index];
              return Card(
                elevation: 4,
                shadowColor: Colors.grey.withOpacity(0.3),
                margin: const EdgeInsets.only(bottom: 16),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              skill.name,
                              style: Theme.of(context).textTheme.titleLarge,
                            ),
                          ),
                          if (widget.isOwner)
                            PopupMenuButton<String>(
                              onSelected: (value) {
                                switch (value) {
                                  case 'edit':
                                    _showAddEditDialog(skill);
                                    break;
                                  case 'delete':
                                    _confirmDelete(skill);
                                    break;
                                }
                              },
                              itemBuilder: (context) => [
                                const PopupMenuItem(
                                  value: 'edit',
                                  child: Text('تعديل'),
                                ),
                                const PopupMenuItem(
                                  value: 'delete',
                                  child: Text('حذف'),
                                ),
                              ],
                            ),
                        ],
                      ),
                      if (skill.description.isNotEmpty) ...[
                        const SizedBox(height: 8),
                        Text(skill.description),
                      ],
                      const SizedBox(height: 8),
                      _buildSkillLevel(skill.level),
                    ],
                  ),
                ),
              );
            },
          );
        }),
      ),
      floatingActionButton: widget.isOwner
          ? FloatingActionButton(
              onPressed: () => _showAddEditDialog(),
              tooltip: 'إضافة مهارة',
              child: const Icon(Icons.add),
            )
          : null,
    );
  }
}

class LevelFormField extends StatefulWidget {
  const LevelFormField(
      {super.key, this.validator, this.initialValue, this.onChanged});
  final String? Function(int?)? validator;
  final int? initialValue;
  final void Function(int)? onChanged;

  @override
  State<LevelFormField> createState() => _LevelFormFieldState();
}

class _LevelFormFieldState extends State<LevelFormField> {
  int _selectedLevel = 0;
  @override
  void initState() {
    _selectedLevel = widget.initialValue ?? 0;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'مستوى المهارة',
          style: Theme.of(context).textTheme.bodyLarge,
        ),
        const SizedBox(height: 8),
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            activeTrackColor: Theme.of(context).colorScheme.primary,
            inactiveTrackColor:
                Theme.of(context).colorScheme.surfaceContainerHighest,
            thumbColor: Theme.of(context).colorScheme.primary,
            overlayColor:
                Theme.of(context).colorScheme.primary.withOpacity(0.2),
            valueIndicatorColor: Theme.of(context).colorScheme.primary,
            showValueIndicator: ShowValueIndicator.always,
          ),
          child: Slider(
            value: _selectedLevel.toDouble(),
            min: 0,
            max: 100,
            label: "%${_selectedLevel.toInt()}",
            // divisions: 1,
            onChanged: (value) {
              setState(() {
                _selectedLevel = value.toInt();
              });
              widget.onChanged?.call(_selectedLevel);
            },
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              "مبتدئ",
              style: Theme.of(context).textTheme.bodySmall,
            ),
            Text(
              "محترف",
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
      ],
    );
  }
}

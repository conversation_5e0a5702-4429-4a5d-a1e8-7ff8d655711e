import 'package:almashal/src/controllers/excellence_award_controller.dart';
import 'package:almashal/src/view/components/layouts/master_page.dart';
import 'package:almashal/src/view/pages/excellence_award/components/excellence_award_list_view.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

class ExcellenceAwardsPage extends StatefulWidget {
  const ExcellenceAwardsPage({super.key});

  @override
  State<ExcellenceAwardsPage> createState() => _ExcellenceAwardsPageState();
}

class _ExcellenceAwardsPageState extends State<ExcellenceAwardsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  int _currentTabIndex = 0;

  // Define award types
  final List<Map<String, String>> awardTypes = [
    {
      'key': 'academic_excellence',
      'title': 'التفوق الأكاديمي',
    },
    {
      'key': 'quran_memorization',
      'title': 'حفظ القرآن',
    },
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: awardTypes.length, vsync: this);
    _tabController.addListener(() {
      if (!_tabController.indexIsChanging) {
        setState(() {
          _currentTabIndex = _tabController.index;
        });
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// Get the current active controller based on tab index
  ExcellenceAwardController? _getCurrentController() {
    try {
      return Get.find<ExcellenceAwardController>(
        tag: 'type_${awardTypes[_currentTabIndex]['key']}',
      );
    } catch (e) {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return MasterPage(
      title: 'جائزة التفوق',
      isSearchable: true,
      onSearchSubmitted: (value) {
        if (value != null) {
          final controller = _getCurrentController();
          controller?.search(value);
        }
      },
      onSearchToggled: (value) {
        if (!value) {
          final controller = _getCurrentController();
          controller?.clearSearch();
        }
      },
      body: Column(
        children: [
          TabBar(
            controller: _tabController,
            labelStyle: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              fontFamily: GoogleFonts.tajawalTextTheme().titleSmall?.fontFamily,
            ),
            unselectedLabelStyle: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              fontFamily: GoogleFonts.tajawalTextTheme().titleSmall?.fontFamily,
            ),
            tabs: List.generate(awardTypes.length, (index) {
              return Tab(
                text: awardTypes[index]['title'],
              );
            }),
          ),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: List.generate(
                awardTypes.length,
                (index) {
                  return ExcellenceAwardListView(
                    type: awardTypes[index]['key']!,
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}

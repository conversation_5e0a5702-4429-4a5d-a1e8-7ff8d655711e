import 'package:almashal/src/controllers/excellence_award_controller.dart';
import 'package:almashal/src/data/enums/page_loading_status.dart';
import 'package:almashal/src/view/components/errors/loading.dart';
import 'package:almashal/src/view/components/errors/no_content.dart';
import 'package:almashal/src/view/components/errors/no_internet.dart';
import 'package:almashal/src/view/components/errors/server_error.dart';
import 'package:almashal/src/view/components/list_tiles/excellence_award_list_tile.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ExcellenceAwardListView extends StatelessWidget {
  const ExcellenceAwardListView({
    super.key,
    required this.type,
    this.withSearchField = false,
  });

  final String type;
  final bool withSearchField;

  @override
  Widget build(BuildContext context) {
    var controller = Get.put<ExcellenceAwardController>(
      ExcellenceAwardController(type: type),
      tag: 'type_$type',
    );

    return Obx(
      () {
        if (controller.pageLoadingStatus.value == PageLoadingStatus.loading) {
          return const Loading();
        }
        if (controller.pageLoadingStatus.value ==
            PageLoadingStatus.networkError) {
          return const NoInternet();
        }
        if (controller.pageLoadingStatus.value == PageLoadingStatus.failed) {
          return const ServerError();
        }
        var awards = controller.excellenceAwards.value;
        if (awards.isEmpty) {
          String message = _getEmptyMessage(type);
          if (controller.searchTerm.value?.isNotEmpty ?? false) {
            message =
                _getSearchEmptyMessage(type, controller.searchTerm.value!);
          }
          return NoContent(
            message: message,
          );
        }
        return ListView.builder(
          itemCount: awards.length,
          padding: const EdgeInsets.all(16),
          itemBuilder: (BuildContext context, int index) {
            return AwardListTitle(
              award: awards[index],
              last: index == awards.length - 1,
            );
          },
        );
      },
    );
  }

  /// Get type-specific empty message
  String _getEmptyMessage(String type) {
    switch (type) {
      case 'academic_excellence':
        return "لا توجد جوائز تفوق أكاديمي متاحة حالياً.\nتابعونا للاطلاع على آخر الجوائز والإنجازات.";
      case 'quran_memorization':
        return "لا توجد جوائز حفظ القرآن الكريم متاحة حالياً.\nتابعونا للاطلاع على آخر الجوائز والإنجازات.";
      default:
        return "لا توجد جوائز تفوق متاحة حالياً.\nتابعونا للاطلاع على آخر الجوائز والإنجازات.";
    }
  }

  /// Get type-specific search empty message
  String _getSearchEmptyMessage(String type, String searchTerm) {
    switch (type) {
      case 'academic_excellence':
        return "لم يتم العثور على نتائج في جوائز التفوق الأكاديمي تطابق كلمة البحث '$searchTerm'.\nجرب استخدام كلمات مختلفة أو تصفح جميع الجوائز.";
      case 'quran_memorization':
        return "لم يتم العثور على نتائج في جوائز حفظ القرآن الكريم تطابق كلمة البحث '$searchTerm'.\nجرب استخدام كلمات مختلفة أو تصفح جميع الجوائز.";
      default:
        return "لم يتم العثور على نتائج تطابق كلمة البحث '$searchTerm'.\nجرب استخدام كلمات مختلفة أو تصفح جميع الجوائز.";
    }
  }
}

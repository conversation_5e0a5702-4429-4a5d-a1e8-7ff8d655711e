import 'package:almashal/src/data/models/family_tree_node.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../../controllers/family_tree_connection_controller.dart';
import '../../../core/routes/app_pages.dart';
import '../../components/layouts/master_page.dart';
import '../../components/form_fields/app_text_form_field.dart';
import '../../components/buttons/custom_filled_button.dart';

class ConnectionRequestFormPage
    extends GetView<FamilyTreeConnectionController> {
  const ConnectionRequestFormPage({super.key});

  @override
  Widget build(BuildContext context) {
    final formKey = GlobalKey<FormState>();
    final selectedNode = Get.arguments as FamilyTreeNode;
    controller.selectedNode(selectedNode);
    return MasterPage(
      title: 'طلب ربط الملف الشخصي',
      body: Obx(() {
        final selectedNode = controller.selectedNode.value;

        if (selectedNode == null) {
          return const Center(
            child: Text('لم يتم اختيار عضو من شجرة العائلة'),
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Selected Node Information Card
                Card(
                  elevation: 4,
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'العضو المختار',
                          style:
                              Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: Theme.of(context).primaryColor,
                                  ),
                        ),
                        const SizedBox(height: 16),
                        Row(
                          children: [
                            // Profile Image
                            Container(
                              width: 80,
                              height: 80,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(40),
                                border: Border.all(
                                  color: Theme.of(context).primaryColor,
                                  width: 2,
                                ),
                              ),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(38),
                                child: selectedNode.image != null &&
                                        selectedNode.image!.isNotEmpty
                                    ? CachedNetworkImage(
                                        imageUrl: selectedNode.image!,
                                        fit: BoxFit.cover,
                                        placeholder: (context, url) =>
                                            Container(
                                          color: Colors.grey[200],
                                          child: const Icon(
                                            Icons.person,
                                            color: Colors.grey,
                                            size: 40,
                                          ),
                                        ),
                                        errorWidget: (context, url, error) =>
                                            Container(
                                          color: Colors.grey[200],
                                          child: const Icon(
                                            Icons.person,
                                            color: Colors.grey,
                                            size: 40,
                                          ),
                                        ),
                                      )
                                    : Container(
                                        color: Colors.grey[200],
                                        child: const Icon(
                                          Icons.person,
                                          color: Colors.grey,
                                          size: 40,
                                        ),
                                      ),
                              ),
                            ),

                            const SizedBox(width: 16),

                            // Node Details
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    selectedNode.name +
                                        (selectedNode.nickName != null &&
                                                selectedNode
                                                    .nickName!.isNotEmpty
                                            ? " (${selectedNode.nickName})"
                                            : ""),
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleLarge
                                        ?.copyWith(
                                          fontWeight: FontWeight.bold,
                                        ),
                                  ),
                                  if (selectedNode.nickName != null &&
                                      selectedNode.nickName!.isNotEmpty) ...[
                                    const SizedBox(height: 4),
                                    Text(
                                      'الكنية: ${selectedNode.nickName}',
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodyMedium
                                          ?.copyWith(
                                            color:
                                                Theme.of(context).primaryColor,
                                          ),
                                    ),
                                  ],
                                  const SizedBox(height: 8),
                                  Row(
                                    children: [
                                      Container(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 8,
                                          vertical: 4,
                                        ),
                                        decoration: BoxDecoration(
                                          color: selectedNode.alive == 1
                                              ? Colors.green.withOpacity(0.1)
                                              : Colors.grey.withOpacity(0.1),
                                          borderRadius:
                                              BorderRadius.circular(12),
                                        ),
                                        child: Text(
                                          selectedNode.alive == 1
                                              ? 'على قيد الحياة'
                                              : 'متوفى',
                                          style: Theme.of(context)
                                              .textTheme
                                              .bodySmall
                                              ?.copyWith(
                                                color: selectedNode.alive == 1
                                                    ? Colors.green
                                                    : Colors.grey,
                                              ),
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      if (selectedNode.gender != null)
                                        Icon(
                                          selectedNode.gender == 1
                                              ? Icons.male
                                              : Icons.female,
                                          size: 20,
                                          color: selectedNode.gender == 1
                                              ? Colors.blue
                                              : Colors.pink,
                                        ),
                                    ],
                                  ),
                                  if (selectedNode.job != null &&
                                      selectedNode.job!.isNotEmpty) ...[
                                    const SizedBox(height: 8),
                                    Text(
                                      selectedNode.job!,
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodyMedium,
                                    ),
                                  ],
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 24),

                // Request Form
                Text(
                  'تفاصيل الطلب',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),

                const SizedBox(height: 16),

                AppTextFormField(
                  controller: controller.noteController,
                  labelText: 'سبب طلب الربط *',
                  hintText: 'اشرح سبب طلبك لربط ملفك الشخصي بهذا العضو...',
                  maxLines: 5,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'يرجى إدخال سبب طلب الربط';
                    }
                    if (value.trim().length < 10) {
                      return 'يجب أن يكون السبب 10 أحرف على الأقل';
                    }
                    return null;
                  },
                  filled: true,
                  fillColor: Colors.grey[50],
                ),

                const SizedBox(height: 16),

                // Info Card
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.blue.withOpacity(0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: Colors.blue[700],
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'سيتم مراجعة طلبك من قبل إدارة الموقع. ستتلقى إشعاراً عند الموافقة على الطلب أو رفضه.',
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: Colors.blue[700],
                                  ),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 32),

                // Submit Button
                SizedBox(
                  width: double.infinity,
                  child: Obx(() => CustomFilledButton(
                        label: controller.isSubmittingRequest.value
                            ? 'جاري الإرسال...'
                            : 'إرسال الطلب',
                        onTap: controller.isSubmittingRequest.value
                            ? () async {}
                            : () async {
                                if (formKey.currentState?.validate() ?? false) {
                                  final success = await controller
                                      .submitConnectionRequest();
                                }
                              },
                      )),
                ),

                const SizedBox(height: 16),

                // Cancel Button
                SizedBox(
                  width: double.infinity,
                  child: TextButton(
                    onPressed: () => Get.back(),
                    child: const Text('إلغاء'),
                  ),
                ),
              ],
            ),
          ),
        );
      }),
    );
  }
}

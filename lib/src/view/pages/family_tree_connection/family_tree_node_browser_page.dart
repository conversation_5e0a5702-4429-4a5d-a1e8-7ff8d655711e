import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../controllers/family_tree_connection_controller.dart';
import '../../../data/enums/page_loading_status.dart';
import '../../../core/routes/app_pages.dart';
import '../../components/layouts/master_page.dart';
import '../../components/cards/family_tree_node_card.dart';
import '../../components/errors/loading.dart';
import '../../components/errors/no_content.dart';
import '../../components/errors/no_internet.dart';
import '../../components/errors/server_error.dart';
import '../../components/form_fields/app_text_form_field.dart';

class FamilyTreeNodeBrowserPage
    extends GetView<FamilyTreeConnectionController> {
  const FamilyTreeNodeBrowserPage({super.key});

  @override
  Widget build(BuildContext context) {
    return MasterPage(
      title: 'ربط الملف الشخصي',
      body: Column(
        children: [
          // Search Section
          Container(
            padding: const EdgeInsets.all(16),
            color: Theme.of(context).cardColor,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'البحث عن عضو في شجرة العائلة',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: 8),
                Text(
                  'ابحث عن العضو الذي تريد ربط ملفك الشخصي به',
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
                ),
                const SizedBox(height: 16),
                AppTextFormField(
                  controller: controller.searchController,
                  hintText: 'ابحث بالاسم أو الكنية...',
                  prefixIcon: const Icon(Icons.search),
                  suffixIcon: Obx(
                    () => controller.searchQuery.value.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: controller.clearSearch,
                          )
                        : const SizedBox.shrink(),
                  ),
                  onChanged: (value) {
                    controller.updateSearchQuery(value ?? "");
                  },
                  filled: true,
                  fillColor: Colors.grey[50],
                ),
              ],
            ),
          ),

          // Results Section
          Expanded(
            child: Obx(() {
              switch (controller.pageLoadingStatus.value) {
                case PageLoadingStatus.loading:
                  return const Loading();
                case PageLoadingStatus.networkError:
                  return const NoInternet();
                case PageLoadingStatus.failed:
                  return const ServerError();
                case PageLoadingStatus.done:
                  if (controller.familyTreeNodes.isEmpty) {
                    return NoContent(
                      message: controller.searchQuery.value.isNotEmpty
                          ? 'لم يتم العثور على نتائج للبحث'
                          : 'لا توجد أعضاء في شجرة العائلة',
                    );
                  }

                  return RefreshIndicator(
                    onRefresh: () =>
                        controller.loadFamilyTreeNodes(refresh: true),
                    child: ListView.builder(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      itemCount: controller.familyTreeNodes.length +
                          (controller.hasNextPage.value ? 1 : 0),
                      itemBuilder: (context, index) {
                        // Load more indicator
                        if (index == controller.familyTreeNodes.length) {
                          if (controller.isLoadingMore.value) {
                            return const Padding(
                              padding: EdgeInsets.all(16),
                              child: Center(child: CircularProgressIndicator()),
                            );
                          } else {
                            // Trigger load more when this item becomes visible
                            WidgetsBinding.instance.addPostFrameCallback((_) {
                              controller.loadMoreNodes();
                            });
                            return const SizedBox.shrink();
                          }
                        }

                        final node = controller.familyTreeNodes[index];
                        return FamilyTreeNodeCard(
                          node: node,
                          onTap: () {
                            controller.selectNode(node);
                            Get.toNamed(Routes.CONNECTION_REQUEST_FORM_PAGE);
                          },
                          showSelectionIndicator: false,
                        );
                      },
                    ),
                  );
              }
            }),
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../../controllers/family_tree_connection_controller.dart';
import '../../../data/enums/page_loading_status.dart';
import '../../../data/models/family_tree_connection_request.dart';
import '../../../core/routes/app_pages.dart';
import '../../components/layouts/master_page.dart';
import '../../components/chips/connection_request_status_chip.dart';
import '../../components/errors/loading.dart';
import '../../components/errors/no_content.dart';
import '../../components/errors/no_internet.dart';
import '../../components/errors/server_error.dart';

class ConnectionRequestStatusPage
    extends GetView<FamilyTreeConnectionController> {
  const ConnectionRequestStatusPage({super.key});

  @override
  Widget build(BuildContext context) {
    // Load connection requests when page opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.loadConnectionRequests(refresh: true);
    });

    return MasterPage(
      title: 'حالة طلبات الربط',
      body: Column(
        children: [
          // Header Section
          Container(
            padding: const EdgeInsets.all(16),
            color: Theme.of(context).cardColor,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'طلبات ربط الملف الشخصي',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'تابع حالة طلباتك لربط ملفك الشخصي بأعضاء شجرة العائلة',
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
                ),
              ],
            ),
          ),

          // Requests List
          Expanded(
            child: Obx(() {
              switch (controller.requestsPageLoadingStatus.value) {
                case PageLoadingStatus.loading:
                  return const Loading();
                case PageLoadingStatus.networkError:
                  return const NoInternet();
                case PageLoadingStatus.failed:
                  return const ServerError();
                case PageLoadingStatus.done:
                  if (controller.connectionRequests.isEmpty) {
                    return const NoContent(message: 'لا توجد طلبات ربط');
                  }

                  return RefreshIndicator(
                    onRefresh: () =>
                        controller.loadConnectionRequests(refresh: true),
                    child: ListView.builder(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      itemCount:
                          controller.connectionRequests.length +
                          (controller.requestsHasNextPage.value ? 1 : 0),
                      itemBuilder: (context, index) {
                        // Load more indicator
                        if (index == controller.connectionRequests.length) {
                          if (controller.isLoadingMoreRequests.value) {
                            return const Padding(
                              padding: EdgeInsets.all(16),
                              child: Center(child: CircularProgressIndicator()),
                            );
                          } else {
                            // Trigger load more when this item becomes visible
                            WidgetsBinding.instance.addPostFrameCallback((_) {
                              controller.loadMoreRequests();
                            });
                            return const SizedBox.shrink();
                          }
                        }

                        final request = controller.connectionRequests[index];
                        return _buildRequestCard(context, request);
                      },
                    ),
                  );
              }
            }),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => Get.toNamed(Routes.FAMILY_TREE_NODE_BROWSER_PAGE),
        icon: const Icon(Icons.add),
        label: const Text('طلب جديد'),
      ),
    );
  }

  Widget _buildRequestCard(
    BuildContext context,
    FamilyTreeConnectionRequest request,
  ) {
    final dateFormat = DateFormat('dd/MM/yyyy - HH:mm', 'ar');

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with status
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'طلب ربط #${request.id}',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                ConnectionRequestStatusChip(status: request.status),
              ],
            ),

            const SizedBox(height: 12),

            // Family tree node info
            if (request.familyTreeNode != null) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.person, color: Colors.grey),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        request.familyTreeNode!.name,
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),
            ],

            // Note
            if (request.note != null && request.note!.isNotEmpty) ...[
              Text(
                'سبب الطلب:',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[700],
                ),
              ),
              const SizedBox(height: 4),
              Text(
                request.note!,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 12),
            ],

            // Dates
            Row(
              children: [
                Icon(Icons.schedule, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  'تاريخ الطلب: ${dateFormat.format(request.requestedAt)}',
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                ),
              ],
            ),

            if (request.reviewedAt != null) ...[
              const SizedBox(height: 4),
              Row(
                children: [
                  Icon(Icons.check_circle, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    'تاريخ المراجعة: ${dateFormat.format(request.reviewedAt!)}',
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }
}

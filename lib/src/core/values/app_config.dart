import 'package:almashal/src/data/services/auth_service.dart';
import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';

import '../../data/providers/api/api_provider.dart';

class AppConfig {
  AppConfig.__();

  // static const String baseUrl = 'https://externally-harmless-sculpin.ngrok-free.app';
  // static const String baseUrl = 'https://almashal.smart-fingers.com';
  static const String baseUrl = 'https://almashalfamily.com';
  // static const String baseUrl = 'https://almashalfamily.test';
  // api url
  static const String apiUrl = '$baseUrl/api/v1';
  // image base url
  static const String imageBaseUrl = '$baseUrl/uploads/';
  // thumb base url
  static const String thumbBaseUrl = '$baseUrl/images/thumb/';
  // one signal app id
  static const String oneSignalAppId = '************************************';

  static Dio get dioInstance {
    var dio = Dio(BaseOptions(baseUrl: apiUrl, headers: {
      "Content-Type": "application/json",
      "Accept": "application/json",
      'api-key': "NwaAi8q5SXQAu9P5X3bqSPGkakoI"
    }));

    dio.interceptors.add(PrettyDioLogger(
      requestHeader: true,
      request: true,
    ));
    return dio;
  }

  static Dio get authenticatedDioInstance {
    // Safe access to AuthService with null check
    String? token;
    try {
      final authService = Get.find<AuthService>();
      token = authService.userData.value?.token;
    } catch (e) {
      // AuthService not initialized yet, use empty token
      token = null;
    }

    var dio = Dio(BaseOptions(baseUrl: apiUrl, headers: {
      "Content-Type": "application/json",
      "Accept": "application/json",
      'api-key': "NwaAi8q5SXQAu9P5X3bqSPGkakoI",
      if (token != null) 'Authorization': 'Bearer $token',
    }));
    dio.interceptors.add(PrettyDioLogger(
      requestHeader: true,
      request: true,
    ));

    return dio;
  }

  static ApiProvider get apiProvider => ApiProvider(
        dioInstance,
        baseUrl: apiUrl,
      );
  static ApiProvider get authenticatedApiProvider => ApiProvider(
        authenticatedDioInstance,
        baseUrl: apiUrl,
      );
}

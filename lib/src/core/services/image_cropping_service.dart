import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:get/get.dart';
import '../utils/platform_helper.dart';

class ImageCroppingService {
  static const int maxImageSize = 1024; // Maximum width/height in pixels
  static const int imageQuality = 85; // JPEG quality (0-100)

  /// Crops an image to a square (1:1) aspect ratio
  /// Returns the cropped image file or null if cancelled
  static Future<File?> cropImageToSquare({
    required XFile imageFile,
    String title = 'قص الصورة',
  }) async {
    try {
      // Check if the image file exists and is valid
      if (!await File(imageFile.path).exists()) {
        Get.snackbar(
          'خطأ',
          'الملف المحدد غير موجود',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return null;
      }

      // For web platform, handle differently
      if (PlatformHelper.isWeb) {
        return await _cropImageForWeb(imageFile, title);
      }

      // For mobile platforms
      return await _cropImageForMobile(imageFile, title);
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء قص الصورة: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return null;
    }
  }

  /// Crop image for mobile platforms using image_cropper
  static Future<File?> _cropImageForMobile(
      XFile imageFile, String title) async {
    final croppedFile = await ImageCropper().cropImage(
      sourcePath: imageFile.path,
      aspectRatio: const CropAspectRatio(ratioX: 1.0, ratioY: 1.0),
      uiSettings: [
        AndroidUiSettings(
          toolbarTitle: title,
          toolbarColor: Get.theme.primaryColor,
          toolbarWidgetColor: Colors.white,
          initAspectRatio: CropAspectRatioPreset.square,
          lockAspectRatio: true,
          aspectRatioPresets: [CropAspectRatioPreset.square],
          showCropGrid: true,
          hideBottomControls: false,
          cropGridStrokeWidth: 2,
          cropGridColor: Get.theme.primaryColor,
          activeControlsWidgetColor: Get.theme.primaryColor,
        ),
        IOSUiSettings(
          title: title,
          doneButtonTitle: 'تم',
          cancelButtonTitle: 'إلغاء',
          aspectRatioLockEnabled: true,
          resetAspectRatioEnabled: false,
          aspectRatioPickerButtonHidden: true,
          rotateButtonsHidden: false,
          rotateClockwiseButtonHidden: false,
          hidesNavigationBar: false,
          rectX: 0,
          rectY: 0,
          rectWidth: 0,
          rectHeight: 0,
        ),
        WebUiSettings(
          context: Get.context!,
          presentStyle: WebPresentStyle.dialog,
          size: const CropperSize(
            width: 520,
            height: 520,
          ),
          dragMode: WebDragMode.move,
          initialAspectRatio: 1.0,
        ),
      ],
      compressFormat: ImageCompressFormat.jpg,
      compressQuality: imageQuality,
      maxWidth: maxImageSize,
      maxHeight: maxImageSize,
    );

    if (croppedFile != null) {
      return File(croppedFile.path);
    }
    return null;
  }

  /// Crop image for web platform
  static Future<File?> _cropImageForWeb(XFile imageFile, String title) async {
    // For web, we'll use the same image_cropper but with web-specific settings
    final croppedFile = await ImageCropper().cropImage(
      sourcePath: imageFile.path,
      aspectRatio: const CropAspectRatio(ratioX: 1.0, ratioY: 1.0),
      uiSettings: [
        WebUiSettings(
          context: Get.context!,
          presentStyle: WebPresentStyle.dialog,
          size: const CropperSize(
            width: 520,
            height: 520,
          ),
          dragMode: WebDragMode.move,
          initialAspectRatio: 1.0,
        ),
      ],
      compressFormat: ImageCompressFormat.jpg,
      compressQuality: imageQuality,
      maxWidth: maxImageSize,
      maxHeight: maxImageSize,
    );

    if (croppedFile != null) {
      return File(croppedFile.path);
    }
    return null;
  }

  /// Validates if an image needs cropping (checks if it's already square)
  static Future<bool> needsCropping(XFile imageFile) async {
    try {
      // For a more accurate check, we would need to decode the image
      // For now, we'll assume all images need cropping for consistency
      return true;
    } catch (e) {
      // If we can't read the image, assume it needs cropping
      return true;
    }
  }

  /// Shows a preview dialog before cropping
  static Future<bool> showCroppingConfirmation({
    required String message,
    String title = 'قص الصورة',
    String confirmText = 'قص الصورة',
    String cancelText = 'إلغاء',
  }) async {
    final result = await Get.dialog<bool>(
      AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: Text(cancelText),
          ),
          ElevatedButton(
            onPressed: () => Get.back(result: true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Get.theme.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: Text(confirmText),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  /// Optimizes image size and quality for upload
  static Future<File?> optimizeImageForUpload(File imageFile) async {
    try {
      // The image_cropper already handles optimization during cropping
      // Additional optimization can be added here if needed
      return imageFile;
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحسين الصورة: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return null;
    }
  }

  /// Shows loading dialog during cropping process
  static void showCroppingLoader() {
    Get.dialog(
      const Center(
        child: Card(
          child: Padding(
            padding: EdgeInsets.all(20.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('جاري قص الصورة...'),
              ],
            ),
          ),
        ),
      ),
      barrierDismissible: false,
    );
  }

  /// Hides the loading dialog
  static void hideCroppingLoader() {
    if (Get.isDialogOpen ?? false) {
      Get.back();
    }
  }
}

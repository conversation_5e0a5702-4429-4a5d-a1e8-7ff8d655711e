import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';

/// Enhanced Debug Service for comprehensive logging throughout the app
/// Provides structured logging with different levels and categories
class DebugService {
  static const String _appName = 'AlmashalFamily';
  
  // Log levels
  static const int _levelVerbose = 500;
  static const int _levelDebug = 700;
  static const int _levelInfo = 800;
  static const int _levelWarning = 900;
  static const int _levelError = 1000;
  
  // Categories
  static const String categoryGeneral = 'General';
  static const String categoryAuth = 'Auth';
  static const String categoryProfile = 'Profile';
  static const String categoryImageCropper = 'ImageCropper';
  static const String categoryNetwork = 'Network';
  static const String categoryUI = 'UI';
  static const String categoryNavigation = 'Navigation';
  static const String categoryStorage = 'Storage';

  /// Log verbose messages (detailed debugging info)
  static void verbose(String message, {String category = categoryGeneral, Object? data}) {
    if (kDebugMode) {
      final formattedMessage = _formatMessage(message, data);
      developer.log(
        formattedMessage,
        name: '$_appName:$category',
        level: _levelVerbose,
        time: DateTime.now(),
      );
      print('🔍 VERBOSE [$category]: $formattedMessage');
    }
  }

  /// Log debug messages (general debugging info)
  static void debug(String message, {String category = categoryGeneral, Object? data}) {
    if (kDebugMode) {
      final formattedMessage = _formatMessage(message, data);
      developer.log(
        formattedMessage,
        name: '$_appName:$category',
        level: _levelDebug,
        time: DateTime.now(),
      );
      print('🐛 DEBUG [$category]: $formattedMessage');
    }
  }

  /// Log info messages (important information)
  static void info(String message, {String category = categoryGeneral, Object? data}) {
    if (kDebugMode) {
      final formattedMessage = _formatMessage(message, data);
      developer.log(
        formattedMessage,
        name: '$_appName:$category',
        level: _levelInfo,
        time: DateTime.now(),
      );
      print('ℹ️ INFO [$category]: $formattedMessage');
    }
  }

  /// Log warning messages (potential issues)
  static void warning(String message, {String category = categoryGeneral, Object? data}) {
    if (kDebugMode) {
      final formattedMessage = _formatMessage(message, data);
      developer.log(
        formattedMessage,
        name: '$_appName:$category',
        level: _levelWarning,
        time: DateTime.now(),
      );
      print('⚠️ WARNING [$category]: $formattedMessage');
    }
  }

  /// Log error messages (errors and exceptions)
  static void error(String message, {String category = categoryGeneral, Object? error, StackTrace? stackTrace}) {
    final formattedMessage = _formatMessage(message, error);
    developer.log(
      formattedMessage,
      name: '$_appName:$category',
      level: _levelError,
      time: DateTime.now(),
      error: error,
      stackTrace: stackTrace,
    );
    print('❌ ERROR [$category]: $formattedMessage');
    
    if (error != null) {
      print('❌ ERROR DETAILS: $error');
    }
    
    if (stackTrace != null) {
      print('❌ STACK TRACE:\n$stackTrace');
    }
  }

  /// Log network requests and responses
  static void network(String message, {Object? data, bool isRequest = true}) {
    if (kDebugMode) {
      final prefix = isRequest ? '📤 REQUEST' : '📥 RESPONSE';
      final formattedMessage = _formatMessage(message, data);
      developer.log(
        formattedMessage,
        name: '$_appName:${categoryNetwork}',
        level: _levelInfo,
        time: DateTime.now(),
      );
      print('$prefix [${categoryNetwork}]: $formattedMessage');
    }
  }

  /// Log image cropper specific events
  static void imageCropper(String message, {Object? data}) {
    if (kDebugMode) {
      final formattedMessage = _formatMessage(message, data);
      developer.log(
        formattedMessage,
        name: '$_appName:${categoryImageCropper}',
        level: _levelInfo,
        time: DateTime.now(),
      );
      print('📸 IMAGE_CROPPER: $formattedMessage');
    }
  }

  /// Log authentication events
  static void auth(String message, {Object? data}) {
    if (kDebugMode) {
      final formattedMessage = _formatMessage(message, data);
      developer.log(
        formattedMessage,
        name: '$_appName:${categoryAuth}',
        level: _levelInfo,
        time: DateTime.now(),
      );
      print('🔐 AUTH: $formattedMessage');
    }
  }

  /// Log profile-related events
  static void profile(String message, {Object? data}) {
    if (kDebugMode) {
      final formattedMessage = _formatMessage(message, data);
      developer.log(
        formattedMessage,
        name: '$_appName:${categoryProfile}',
        level: _levelInfo,
        time: DateTime.now(),
      );
      print('👤 PROFILE: $formattedMessage');
    }
  }

  /// Log UI events and interactions
  static void ui(String message, {Object? data}) {
    if (kDebugMode) {
      final formattedMessage = _formatMessage(message, data);
      developer.log(
        formattedMessage,
        name: '$_appName:${categoryUI}',
        level: _levelDebug,
        time: DateTime.now(),
      );
      print('🎨 UI: $formattedMessage');
    }
  }

  /// Log navigation events
  static void navigation(String message, {Object? data}) {
    if (kDebugMode) {
      final formattedMessage = _formatMessage(message, data);
      developer.log(
        formattedMessage,
        name: '$_appName:${categoryNavigation}',
        level: _levelInfo,
        time: DateTime.now(),
      );
      print('🧭 NAVIGATION: $formattedMessage');
    }
  }

  /// Log storage operations
  static void storage(String message, {Object? data}) {
    if (kDebugMode) {
      final formattedMessage = _formatMessage(message, data);
      developer.log(
        formattedMessage,
        name: '$_appName:${categoryStorage}',
        level: _levelInfo,
        time: DateTime.now(),
      );
      print('💾 STORAGE: $formattedMessage');
    }
  }

  /// Format message with optional data
  static String _formatMessage(String message, Object? data) {
    if (data == null) return message;
    return '$message | Data: $data';
  }

  /// Log method entry (for debugging method calls)
  static void methodEntry(String className, String methodName, {Object? parameters}) {
    if (kDebugMode) {
      final message = parameters != null 
          ? '$className.$methodName() called with: $parameters'
          : '$className.$methodName() called';
      verbose(message, category: categoryGeneral);
    }
  }

  /// Log method exit (for debugging method calls)
  static void methodExit(String className, String methodName, {Object? result}) {
    if (kDebugMode) {
      final message = result != null 
          ? '$className.$methodName() completed with result: $result'
          : '$className.$methodName() completed';
      verbose(message, category: categoryGeneral);
    }
  }

  /// Test all debug output methods
  static void testAllMethods() {
    if (kDebugMode) {
      print('\n🧪 Testing DebugService - All methods should appear in console:\n');
      
      verbose('This is a verbose message');
      debug('This is a debug message');
      info('This is an info message');
      warning('This is a warning message');
      error('This is an error message');
      
      network('GET /api/users', isRequest: true);
      network('200 OK - Users retrieved', isRequest: false);
      
      imageCropper('Starting image crop operation');
      auth('User login attempt');
      profile('Loading user profile data');
      ui('Button clicked: Save Profile');
      navigation('Navigating to Profile Page');
      storage('Saving user preferences');
      
      methodEntry('ProfileController', 'updateProfileImage', parameters: {'imageFile': 'test.jpg'});
      methodExit('ProfileController', 'updateProfileImage', result: 'success');
      
      print('\n🧪 DebugService test completed - Check console output above\n');
    }
  }
}

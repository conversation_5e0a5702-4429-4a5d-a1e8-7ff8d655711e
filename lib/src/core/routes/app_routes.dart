// ignore_for_file: constant_identifier_names

part of './app_pages.dart';

abstract class Routes {
  Routes._();
  // home
  static const HOME_PAGE = '/home';
  // splash
  static const SPLASH_PAGE = '/splash';
  // login
  static const LOGIN_PAGE = '/login';
  // register
  static const REGISTER_PAGE = '/register';
  // registration pending
  static const REGISTRATION_PENDING_PAGE = '/registration-pending';
  // forgot_password
  static const FORGET_PASSWORD_PAGE = '/forgot_password';
  //  reset_password
  static const RESET_PASSWORD_PAGE = '/reset_password';
  // family tree
  static const FAMILY_TREE_PAGE = '/family-tree';
  // parent
  static const PARENT_PAGE = '/parent';
  // occasions
  static const OCCASIONS_PAGE = '/occasions';
  // news
  static const NEWS_PAGE = '/news';
  // memory
  static const MEMORY_PAGE = '/memory';
  // committees
  static const COMMITTEES_PAGE = '/committees';
  // news detail
  static const NEWS_DETAIL_PAGE = '/news-detail';
  // album detail
  static const ALBUM_DETAIL_PAGE = '/album-detail';
  // image view
  static const IMAGE_VIEW_PAGE = '/image-view';
  // video view
  static const VIDEO_VIEW_PAGE = '/video-view';
  // occasion detail
  static const OCCASION_DETAIL_PAGE = '/occasion-detail';
  // excellence awards
  static const EXCELLENCE_AWARDS_PAGE = '/excellence-awards';
  // excellence awards detail
  static const EXCELLENCE_AWARD_DETAIL_PAGE = '/excellence-award-detail';
  // profile
  static const PROFILE_PAGE = '/profile';
  // profile edit
  static const PROFILE_EDIT_PAGE = '/profile-edit';
  // pdf view
  static const PDF_VIEW_PAGE = '/pdf-view';
  static const FAMILY_INTERACTIVE_TREE_PAGE = '/family-interactive-tree';
  // family tree connection
  static const FAMILY_TREE_NODE_BROWSER_PAGE = '/family-tree-node-browser';
  static const CONNECTION_REQUEST_FORM_PAGE = '/connection-request-form';
  static const CONNECTION_REQUEST_STATUS_PAGE = '/connection-request-status';
}

import 'package:almashal/src/data/services/error_service.dart';
import 'package:almashal/src/view/components/image/image_view.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' as intl;
import 'dart:io';

class CommonFunctions {
  static bool isDirectionRTL() {
    return intl.Bidi.isRtlLanguage(Get.locale?.languageCode);
  }

  static void showErrorMessage(String message) {
    Get.snackbar("خطأ".tr, message,
        colorText: Colors.white,
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 5));
  }

  static Future<String?> getDeviceId() async {
    try {
      DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      if (kIsWeb) {
        WebBrowserInfo webInfo = await deviceInfo.webBrowserInfo;
        return webInfo.userAgent;
      } else if (Platform.isAndroid) {
        AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
        return androidInfo.id;
      } else if (Platform.isIOS) {
        IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
        return iosInfo.identifierForVendor;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  static void showSuccessMessage(String message) {
    if (Get.isSnackbarOpen) {
      Get.closeCurrentSnackbar();
    }
    Get.snackbar("نجاح".tr, message,
        colorText: Colors.white,
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2));
  }

  static void handleError(err) {
    if (kDebugMode) {
      Get.log(err.runtimeType.toString());
    }
    if (err.runtimeType == DioException) {
      var error = err as DioException;
      if (error.type == DioExceptionType.badResponse) {
        Get.log(error.response.toString());
        if (error.type == DioExceptionType.badResponse) {
          print(error.response?.statusCode);
          if (error.response?.statusCode == 429) {
            showErrorMessage(error.response?.data['message']);
          } else if (error.response?.statusCode == 400) {
            if (error.response?.data is String) {
              showErrorMessage(error.response?.data.toString() ?? "");
            } else {
              showErrorMessage(
                  (error.response?.data as Map).values.first[0].toString());
            }
          } else if (error.response?.statusCode == 401) {
            Get.log((error.response?.data as Map).toString());
          } else if (error.response?.statusCode == 403) {
            if (kDebugMode) {
              print((error.response?.data as Map).values.first);
            }
            showErrorMessage(error.response?.data['message']);
          } else if (error.response?.statusCode == 422) {
            if (kDebugMode) {
              print((error.response?.data));
            }
            showErrorMessage(error.response?.data['message']);
            ErrorBag.instance.setErrors(error.response?.data['errors'] ?? {});
          } else if (error.response?.statusCode == 404) {
            print(error.response?.data);
            if (error.response?.data['message'] != null) {
              showErrorMessage(error.response?.data['message']);
            } else {
              showErrorMessage("غير قادر على الاتصال بالسرفر".tr);
            }
          } else if (error.response?.statusCode == 500) {
            if (kDebugMode) {
              print(error.response?.data);
            }
            showErrorMessage("عذراً حدث خطأ في السرفر".tr);
          } else if (error.response?.statusCode == 503) {
            if (kDebugMode) {
              print(error.response?.data);
            }
            showErrorMessage(
                "غير قادر على الاتصال بالسرفر بالوقت الحالي ... يرجى المحاولة لاحقاً"
                    .tr);
          } else {
            showErrorMessage(error.response?.data[0] ?? "");
            if (kDebugMode) {
              print(error.response?.data);
            }
          }
        } else {
          showErrorMessage("تأكد من اتصالك بالانترنت وعاود المحاولة لاحقاً".tr);
        }
      } else if (error.type == DioExceptionType.unknown) {
        print(error);
        showErrorMessage("تأكد من اتصالك بالانترنت وعاود المحاولة لاحقاً".tr);
      }
    } else {
      showErrorMessage(err.toString());
      if (kDebugMode) {
        // Safely print the stack trace without casting to a specific error type
        if (err is Error && err.stackTrace != null) {
          print(err.stackTrace);
        } else {
          print('Error without stack trace: $err');
        }
      }
    }
  }

  // unfocus nodes
  static void unfocusNodes() {
    Get.focusScope?.unfocus();
  }

  static String? htmlToShareableText(String? html) {
    if (html == null) {
      return null;
    }
    return html.replaceAll(RegExp(r'<[^>]*>|&[^;]+;'), ' ');
  }

  static void showImagePreviewDialog(String imageUrl) {
    Get.dialog(
      ImageView(imageUrl: imageUrl),
      barrierDismissible: false,
      barrierColor: Colors.transparent,
    );
  }

  static Size getTextSize({required String text, TextStyle? style}) {
    final TextPainter textPainter = TextPainter(
        text: TextSpan(text: text, style: style),
        maxLines: 1,
        textDirection: TextDirection.ltr)
      ..layout(minWidth: 0, maxWidth: double.infinity);
    return textPainter.size;
  }
}

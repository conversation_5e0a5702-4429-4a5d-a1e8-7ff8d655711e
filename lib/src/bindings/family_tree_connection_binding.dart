import 'package:get/get.dart';
import '../controllers/family_tree_connection_controller.dart';
import '../data/services/family_tree_connection_service.dart';

class FamilyTreeConnectionBinding extends Bindings {
  @override
  void dependencies() {
    // Register the service
    Get.lazyPut<FamilyTreeConnectionService>(
      () => FamilyTreeConnectionService(),
    );
    
    // Register the controller
    Get.lazyPut<FamilyTreeConnectionController>(
      () => FamilyTreeConnectionController(),
    );
  }
}

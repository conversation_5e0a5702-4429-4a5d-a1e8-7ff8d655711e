import 'package:get/get.dart';

class LoadingService extends GetxService {
  Future<LoadingService> init() async {
    return this;
  }

  static LoadingService get instance {
    try {
      return Get.find<LoadingService>();
    } catch (e) {
      throw Exception(
          'LoadingService not initialized. Make sure to call Get.put(LoadingService()) before accessing LoadingService.instance');
    }
  }

  var isLoading = false.obs;
  void show() {
    isLoading.value = true;
  }

  void hide() {
    isLoading.value = false;
  }

  @override
  void onInit() {
    isLoading.listen((value) {
      if (value) {
        // CommonFunctions.showDialog(
        //     contentPadding: EdgeInsets.zero,
        //     content: Column(
        //       mainAxisSize: MainAxisSize.min,
        //       children: [
        //         SpinKitRipple(
        //           size: 100,
        //           color: Get.theme.primaryColor.withOpacity(0.5),
        //         ),
        //         Padding(
        //           padding: const EdgeInsets.only(top: 8.0, bottom: 10.0),
        //           child: Text(
        //             "loadingMessage".tr,
        //             style: TextStyle(
        //               fontWeight: FontWeight.bold,
        //               color: Get.theme.primaryColor,
        //             ),
        //           ),
        //         )
        //       ],
        //     ),
        //     barrierDismissible: false,
        //     onWillPop: () => Future(() => !isLoading.value));
      } else {
        Get.back();
      }
    });
    super.onInit();
  }
}

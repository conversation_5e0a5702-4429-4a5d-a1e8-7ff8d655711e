import 'package:get/get.dart';
import 'package:dio/dio.dart';
import '../models/responses/family_tree_nodes_response.dart';
import '../models/responses/connection_requests_response.dart';
import '../models/responses/general_response.dart';
import '../models/family_tree_node.dart';
import '../../core/values/app_config.dart';
import '../../core/utils/common_functions.dart';
import 'network_service.dart';

class FamilyTreeConnectionService extends GetxService {
  static FamilyTreeConnectionService get instance => Get.find();

  var apiProvider = AppConfig.authenticatedApiProvider;
  final Dio _dio = AppConfig.authenticatedDioInstance;

  /// Search for family tree nodes with pagination
  Future<FamilyTreeNodesResponse?> searchFamilyTreeNodes({
    String? search,
    int page = 1,
    int perPage = 15,
  }) async {
    FamilyTreeNodesResponse? result;

    await NetworkService.instance.checkConnectivity(
      () async {
        try {
          // For now, use the existing getFamilyTree endpoint and simulate pagination
          final response = await apiProvider.getFamilyTree();

          // Filter nodes based on search query
          List<FamilyTreeNode> filteredNodes = response;
          if (search != null && search.isNotEmpty) {
            filteredNodes = response.where((node) {
              return node.name.toLowerCase().contains(search.toLowerCase()) ||
                  (node.nickName?.toLowerCase().contains(
                            search.toLowerCase(),
                          ) ??
                      false);
            }).toList();
          }

          // Simulate pagination
          final startIndex = (page - 1) * perPage;
          final endIndex = startIndex + perPage;
          final paginatedNodes = filteredNodes.length > startIndex
              ? filteredNodes.sublist(
                  startIndex,
                  endIndex > filteredNodes.length
                      ? filteredNodes.length
                      : endIndex,
                )
              : <FamilyTreeNode>[];

          result = FamilyTreeNodesResponse(
            total: filteredNodes.length,
            currentPage: page,
            lastPage: (filteredNodes.length / perPage).ceil(),
            perPage: perPage,
            nodes: paginatedNodes,
          );
        } catch (err) {
          CommonFunctions.handleError(err);
        }
      },
      () {
        CommonFunctions.showErrorMessage('عذراً .. انت غير متصل بالانترنت');
      },
    );

    return result;
  }

  /// Create a new connection request
  Future<bool> createConnectionRequest({
    required int nodeId,
    required String note,
  }) async {
    bool success = false;

    await NetworkService.instance.checkConnectivity(
      () async {
        try {
          final response = await _dio.post(
            '/family-tree-nodes/$nodeId/connection-requests',
            data: {'note': note},
          );

          if (response.statusCode == 200 || response.statusCode == 201) {
            final responseData = response.data;
            final message = responseData['message'] ?? 'تم إرسال الطلب بنجاح';
            Get.back();
            CommonFunctions.showSuccessMessage(message);
            success = true;
          }
        } catch (err) {
          CommonFunctions.handleError(err);
        }
      },
      () {
        CommonFunctions.showErrorMessage('عذراً .. انت غير متصل بالانترنت');
      },
    );

    return success;
  }

  /// Get user's connection requests with pagination
  Future<ConnectionRequestsResponse?> getMyConnectionRequests({
    int page = 1,
    int perPage = 15,
    String? status,
  }) async {
    ConnectionRequestsResponse? result;

    await NetworkService.instance.checkConnectivity(
      () async {
        try {
          final queryParams = <String, dynamic>{
            'page': page,
            'per_page': perPage,
          };
          if (status != null) {
            queryParams['status'] = status;
          }

          final response = await _dio.get(
            '/my-connection-requests',
            queryParameters: queryParams,
          );

          if (response.statusCode == 200) {
            result = ConnectionRequestsResponse.fromJson(response.data);
          }
        } catch (err) {
          CommonFunctions.handleError(err);
        }
      },
      () {
        CommonFunctions.showErrorMessage('عذراً .. انت غير متصل بالانترنت');
      },
    );

    return result;
  }
}

import 'family_tree_node.dart';

class FamilyTreeConnectionRequest {
  int id;
  int familyMemberId;
  int familyTreeNodeId;
  String status;
  String? note;
  DateTime requestedAt;
  DateTime? reviewedAt;
  int? reviewedBy;
  FamilyTreeNode? familyTreeNode;

  FamilyTreeConnectionRequest({
    required this.id,
    required this.familyMemberId,
    required this.familyTreeNodeId,
    required this.status,
    this.note,
    required this.requestedAt,
    this.reviewedAt,
    this.reviewedBy,
    this.familyTreeNode,
  });

  factory FamilyTreeConnectionRequest.fromJson(Map<String, dynamic> json) =>
      FamilyTreeConnectionRequest(
        id: json["id"],
        familyMemberId: json["family_member_id"],
        familyTreeNodeId: json["family_tree_node_id"],
        status: json["status"],
        note: json["note"],
        requestedAt: DateTime.parse(json["requested_at"]),
        reviewedAt: json["reviewed_at"] != null 
            ? DateTime.parse(json["reviewed_at"]) 
            : null,
        reviewedBy: json["reviewed_by"],
        familyTreeNode: json["family_tree_node"] != null
            ? FamilyTreeNode.fromJson(json["family_tree_node"])
            : null,
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "family_member_id": familyMemberId,
        "family_tree_node_id": familyTreeNodeId,
        "status": status,
        "note": note,
        "requested_at": requestedAt.toIso8601String(),
        "reviewed_at": reviewedAt?.toIso8601String(),
        "reviewed_by": reviewedBy,
        "family_tree_node": familyTreeNode?.toJson(),
      };

  // Status constants
  static const String statusPending = 'pending';
  static const String statusApproved = 'approved';
  static const String statusRejected = 'rejected';

  // Helper methods
  bool get isPending => status == statusPending;
  bool get isApproved => status == statusApproved;
  bool get isRejected => status == statusRejected;

  String get statusDisplayText {
    switch (status) {
      case statusPending:
        return 'قيد المراجعة';
      case statusApproved:
        return 'مقبول';
      case statusRejected:
        return 'مرفوض';
      default:
        return status;
    }
  }
}

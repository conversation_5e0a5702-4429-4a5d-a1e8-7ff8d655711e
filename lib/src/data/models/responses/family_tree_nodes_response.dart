import '../family_tree_node.dart';

class FamilyTreeNodesResponse {
  int total;
  int currentPage;
  int lastPage;
  int perPage;
  List<FamilyTreeNode> nodes;

  FamilyTreeNodesResponse({
    required this.total,
    required this.currentPage,
    required this.lastPage,
    required this.perPage,
    required this.nodes,
  });

  factory FamilyTreeNodesResponse.fromJson(Map<String, dynamic> json) =>
      FamilyTreeNodesResponse(
        total: json["total"] ?? 0,
        currentPage: json["current_page"] ?? 1,
        lastPage: json["last_page"] ?? 1,
        perPage: json["per_page"] ?? 15,
        nodes: json["data"] != null 
            ? List<FamilyTreeNode>.from(json["data"].map((x) => FamilyTreeNode.fromJson(x)))
            : [],
      );

  Map<String, dynamic> toJson() => {
        "total": total,
        "current_page": currentPage,
        "last_page": lastPage,
        "per_page": perPage,
        "data": List<dynamic>.from(nodes.map((x) => x.toJson())),
      };

  bool get hasNextPage => currentPage < lastPage;
  bool get hasPreviousPage => currentPage > 1;
}

import 'package:almashal/src/data/models/meta.dart';
import 'package:almashal/src/data/models/profile/review.dart';

class ReviewsListResponse {
  final List<Review> data;
  final Meta meta;
  final String message;

  ReviewsListResponse({
    required this.data,
    required this.meta,
    required this.message,
  });

  factory ReviewsListResponse.fromJson(Map<String, dynamic> json) {
    return ReviewsListResponse(
      data: (json['data'] as List? ?? [])
          .map((item) => Review.fromJson(item ?? {}))
          .toList(),
      meta: Meta.fromJson(json['meta'] ?? {}),
      message: json['message'] ?? 'Success',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'data': data.map((item) => item.toJson()).toList(),
      'meta': meta.toJson(),
      'message': message,
    };
  }
}

import '../family_tree_connection_request.dart';

class ConnectionRequestsResponse {
  int total;
  int currentPage;
  int lastPage;
  int perPage;
  List<FamilyTreeConnectionRequest> requests;

  ConnectionRequestsResponse({
    required this.total,
    required this.currentPage,
    required this.lastPage,
    required this.perPage,
    required this.requests,
  });

  factory ConnectionRequestsResponse.fromJson(Map<String, dynamic> json) =>
      ConnectionRequestsResponse(
        total: json["total"] ?? 0,
        currentPage: json["current_page"] ?? 1,
        lastPage: json["last_page"] ?? 1,
        perPage: json["per_page"] ?? 15,
        requests: json["data"] != null 
            ? List<FamilyTreeConnectionRequest>.from(
                json["data"].map((x) => FamilyTreeConnectionRequest.fromJson(x))
              )
            : [],
      );

  Map<String, dynamic> toJson() => {
        "total": total,
        "current_page": currentPage,
        "last_page": lastPage,
        "per_page": perPage,
        "data": List<dynamic>.from(requests.map((x) => x.toJson())),
      };

  bool get hasNextPage => currentPage < lastPage;
  bool get hasPreviousPage => currentPage > 1;
}

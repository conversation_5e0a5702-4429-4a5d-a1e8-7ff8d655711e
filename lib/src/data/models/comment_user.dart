class CommentUser {
  int id;
  String displayName;
  String? image;

  CommentUser({
    required this.id,
    required this.displayName,
    this.image,
  });

  factory CommentUser.fromJson(Map<String, dynamic> json) => CommentUser(
        id: json["id"] ?? 0,
        displayName: json["display_name"] ?? 'مستخدم غير معروف',
        image: json["image"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "display_name": displayName,
        "image": image,
      };
}

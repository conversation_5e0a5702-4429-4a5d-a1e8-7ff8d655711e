class ExcellenceAward {
  int id;
  String title;
  String content;
  String createdAt;
  String? shareLink;
  int visitsCount;
  String type;

  ExcellenceAward({
    required this.id,
    required this.title,
    required this.content,
    required this.createdAt,
    required this.shareLink,
    this.visitsCount = 0,
    required this.type,
  });

  factory ExcellenceAward.fromJson(Map<String, dynamic> json) =>
      ExcellenceAward(
        id: json["id"],
        title: json["title"],
        content: json["content"],
        createdAt: json["created_at"],
        shareLink: json["share_link"],
        visitsCount: json["visits_count"] ?? 0,
        type: json["type"] ?? "academic_excellence",
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "title": title,
        "content": content,
        "created_at": createdAt,
        "share_link": shareLink,
        "visits_count": visitsCount,
        "type": type,
      };
}

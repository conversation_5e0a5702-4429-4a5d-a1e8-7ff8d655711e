class Skill {
  final int id;
  final String name;
  final String description;
  final int level;
  Skill({
    required this.id,
    required this.name,
    required this.description,
    required this.level,
  });

  factory Skill.fromJson(Map<String, dynamic> json) {
    return Skill(
      id: int.parse(json['id'].toString()),
      name: json['name'],
      description: json['description'] ?? '',
      level: json['level'] ?? 1,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'level': level,
    };
  }

  Skill copyWith({
    int? id,
    String? name,
    String? description,
    int? level,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Skill(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      level: level ?? this.level,
    );
  }
}

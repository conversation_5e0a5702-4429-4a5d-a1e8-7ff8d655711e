import 'package:almashal/src/data/models/comment_user.dart';

class Review {
  final int id;
  final int rating;
  final String comment;
  final CommentUser reviewer;
  final CommentUser reviewedUser;
  final bool status;
  final String createdAt;
  final String createdAtFormatted;
  final String updatedAt;

  Review({
    required this.id,
    required this.rating,
    required this.comment,
    required this.reviewer,
    required this.reviewedUser,
    required this.status,
    required this.createdAt,
    required this.createdAtFormatted,
    required this.updatedAt,
  });

  factory Review.fromJson(Map<String, dynamic> json) {
    return Review(
      id: json['id'] ?? 0,
      rating: json['rating'] ?? 0,
      comment: json['comment'] ?? '',
      reviewer: CommentUser.fromJson(json['reviewer'] ?? {}),
      reviewedUser: CommentUser.fromJson(json['reviewed_user'] ?? {}),
      status: json['status'] ?? true,
      createdAt: json['created_at'] ?? '',
      createdAtFormatted: json['created_at_formatted'] ?? '',
      updatedAt: json['updated_at'] ?? '',
    );
  }

  Map<String, dynamic> to<PERSON>son() {
    return {
      'id': id,
      'rating': rating,
      'comment': comment,
      'reviewer': reviewer.toJson(),
      'reviewed_user': reviewedUser.toJson(),
      'status': status,
      'created_at': createdAt,
      'created_at_formatted': createdAtFormatted,
      'updated_at': updatedAt,
    };
  }

  /// Check if the current user is the reviewer (can edit/delete)
  bool isReviewerCurrentUser(int currentUserId) {
    return reviewer.id == currentUserId;
  }
}

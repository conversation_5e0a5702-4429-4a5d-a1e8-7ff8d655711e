import 'package:almashal/src/core/utils/common_functions.dart';
import 'package:almashal/src/core/values/app_config.dart';
import 'package:almashal/src/data/models/profile/review.dart';
import 'package:almashal/src/data/models/responses/review_summary_response.dart';
import 'package:almashal/src/data/services/network_service.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';

class ReviewsController extends GetxController {
  static ReviewsController get instance => Get.find<ReviewsController>();

  // قائمة المراجعات
  final reviewsList = <Review>[].obs;

  // ملخص المراجعات
  final reviewSummary = Rxn<ReviewSummaryData>();

  // حالة تحميل الصفحة
  final isLoading = true.obs;
  final isSummaryLoading = false.obs;

  // صفحة API الحالية والأخيرة (للصفحات المتعددة)
  int page = 1;
  int lastPage = 1;

  // معرف المستخدم الذي نعرض مراجعاته
  String userId = '';

  // تعيين معرف المستخدم
  void setUserId(String id) {
    userId = id;
    loadReviews();
    loadReviewSummary();
  }

  // تحميل المراجعات من API
  Future<void> loadReviews() async {
    isLoading.value = true;

    await NetworkService.instance.checkConnectivity(
      () async {
        try {
          // استدعاء API لجلب المراجعات
          final response = await AppConfig.authenticatedApiProvider.getReviews(
            userId: userId,
            page: page,
          );

          if (kDebugMode) {
            print("تم استلام المراجعات: ${response.data.length}");
          }

          // تحديث البيانات
          reviewsList.value = response.data;
          page = response.meta.currentPage;
          lastPage = response.meta.lastPage;

          isLoading.value = false;
        } catch (err) {
          isLoading.value = false;
          Get.log(err.toString());
          CommonFunctions.handleError(err);
        }
      },
      () {
        isLoading.value = false;
        Get.snackbar(
          'خطأ في الاتصال',
          'يرجى التحقق من اتصالك بالإنترنت',
          snackPosition: SnackPosition.BOTTOM,
        );
      },
    );
  }

  // تحميل المزيد من المراجعات (للصفحات التالية)
  Future<void> loadMoreReviews() async {
    if (page < lastPage) {
      page++;

      await NetworkService.instance.checkConnectivity(
        () async {
          try {
            // استدعاء API لجلب صفحة إضافية
            final response =
                await AppConfig.authenticatedApiProvider.getReviews(
              userId: userId,
              page: page,
            );

            // إضافة البيانات الجديدة للقائمة الحالية
            reviewsList.addAll(response.data);
            page = response.meta.currentPage;
            lastPage = response.meta.lastPage;
          } catch (err) {
            CommonFunctions.handleError(err);
          }
        },
        () {
          Get.snackbar(
            'خطأ في الاتصال',
            'يرجى التحقق من اتصالك بالإنترنت',
            snackPosition: SnackPosition.BOTTOM,
          );
        },
      );
    }
  }

  // تحميل ملخص المراجعات
  Future<void> loadReviewSummary() async {
    if (userId.isEmpty) return;

    isSummaryLoading.value = true;

    await NetworkService.instance.checkConnectivity(
      () async {
        try {
          final response = await AppConfig.authenticatedApiProvider
              .getReviewSummary(userId: userId);
          reviewSummary.value = response.data;
          isSummaryLoading.value = false;
        } catch (err) {
          isSummaryLoading.value = false;
          CommonFunctions.handleError(err);
        }
      },
      () {
        isSummaryLoading.value = false;
        Get.snackbar(
          'خطأ في الاتصال',
          'يرجى التحقق من اتصالك بالإنترنت',
          snackPosition: SnackPosition.BOTTOM,
        );
      },
    );
  }

  // إضافة مراجعة جديدة
  Future<void> addReview(Map<String, dynamic> data) async {
    await NetworkService.instance.checkConnectivity(
      () async {
        try {
          // تصحيح اسم المعامل حسب API
          final reviewData = {
            'reviewed_user_id': data['user_id'],
            'rating': data['rating'],
            'comment': data['comment'],
          };

          // استدعاء API لإضافة مراجعة
          final response =
              await AppConfig.authenticatedApiProvider.addReview(reviewData);

          CommonFunctions.showSuccessMessage(response.message);

          // إعادة تحميل المراجعات والملخص لعرض البيانات الجديدة
          loadReviews();
          loadReviewSummary();
        } catch (err) {
          CommonFunctions.handleError(err);
        }
      },
      () {
        Get.snackbar(
          'خطأ في الاتصال',
          'يرجى التحقق من اتصالك بالإنترنت',
          snackPosition: SnackPosition.BOTTOM,
        );
      },
    );
  }

  // تحديث مراجعة موجودة
  Future<void> updateReview(int reviewId, Map<String, dynamic> data) async {
    await NetworkService.instance.checkConnectivity(
      () async {
        try {
          final response = await AppConfig.authenticatedApiProvider
              .updateReview(reviewId, data);

          CommonFunctions.showSuccessMessage(response.message);

          // إعادة تحميل المراجعات والملخص
          loadReviews();
          loadReviewSummary();
        } catch (err) {
          CommonFunctions.handleError(err);
        }
      },
      () {
        Get.snackbar(
          'خطأ في الاتصال',
          'يرجى التحقق من اتصالك بالإنترنت',
          snackPosition: SnackPosition.BOTTOM,
        );
      },
    );
  }

  // حذف مراجعة
  Future<void> deleteReview(int reviewId) async {
    await NetworkService.instance.checkConnectivity(
      () async {
        try {
          final response =
              await AppConfig.authenticatedApiProvider.deleteReview(reviewId);

          CommonFunctions.showSuccessMessage(response.message);

          // إزالة المراجعة من القائمة المحلية
          reviewsList.removeWhere((review) => review.id == reviewId);

          // إعادة تحميل الملخص
          loadReviewSummary();
        } catch (err) {
          CommonFunctions.handleError(err);
        }
      },
      () {
        Get.snackbar(
          'خطأ في الاتصال',
          'يرجى التحقق من اتصالك بالإنترنت',
          snackPosition: SnackPosition.BOTTOM,
        );
      },
    );
  }

  // فحص إذا كان يمكن تحميل المزيد من المراجعات
  bool canLoadMore() {
    return page < lastPage;
  }
}

import 'package:almashal/src/core/values/app_config.dart';
import 'package:almashal/src/data/models/excellence_award.dart';
import 'package:almashal/src/data/models/responses/excellence_award_response.dart';
// import 'package:flutter/foundation.dart';
import 'package:get/get.dart';

import '../core/utils/common_functions.dart';
import '../data/enums/page_loading_status.dart';
import '../data/services/network_service.dart';

class ExcellenceAwardController extends GetxController {
  ExcellenceAwardController({this.type});

  Rx<List<ExcellenceAward>> excellenceAwards = Rx<List<ExcellenceAward>>([]);
  var pageLoadingStatus = PageLoadingStatus.done.obs;
  var apiProvider = AppConfig.apiProvider;
  int page = 0;
  int lastPage = 0;
  String? type;

  Rx<String?> searchTerm = Rx<String?>(null);

  void search(String value) {
    searchTerm.value = value;
    page = 0;
    lastPage = 0;
    getData();
  }

  void clearSearch() {
    searchTerm.value = null;
    page = 0;
    lastPage = 0;
    getData();
  }

  @override
  void onInit() {
    getData();
    super.onInit();
  }

  // get tree image
  void getData() {
    pageLoadingStatus.value = PageLoadingStatus.loading;
    NetworkService.instance.checkConnectivity(
      () async {
        try {
          ExcellenceAwardResponse response;
          if (searchTerm.value == null || searchTerm.value!.isEmpty) {
            response = await apiProvider.getExcellenceAwards(type: type);
          } else {
            response = await apiProvider.searchInExcellenceAwards(
                search: searchTerm.value!, type: type);
          }
          // if (kDebugMode) {
          //   print(response.toJson());
          // }
          excellenceAwards.value = response.excellenceAwards;
          page = response.currentPage;
          lastPage = response.lastPage;
          pageLoadingStatus.value = PageLoadingStatus.done;
        } on Exception catch (_, err) {
          CommonFunctions.handleError(err.runtimeType);
          pageLoadingStatus.value = PageLoadingStatus.failed;
        }
      },
      () {
        pageLoadingStatus.value = PageLoadingStatus.networkError;
      },
    );
  }

  // load more
  void loadMore() {
    print('loading more...');
    if (!cantLoadMore()) {
      NetworkService.instance.checkConnectivity(
        () async {
          try {
            ExcellenceAwardResponse response;
            if (searchTerm.value == null || searchTerm.value!.isEmpty) {
              response = await apiProvider.getExcellenceAwards(
                page: page + 1,
                type: type,
              );
            } else {
              response = await apiProvider.searchInExcellenceAwards(
                page: page + 1,
                type: type,
                search: searchTerm.value!,
              );
            }
            excellenceAwards.value.addAll(response.excellenceAwards);
            excellenceAwards.refresh();
            page = response.currentPage;
            lastPage = response.lastPage;
            pageLoadingStatus.value = PageLoadingStatus.done;
          } catch (err) {
            CommonFunctions.handleError(err);
          }
        },
        () {},
      );
    }
  }

  bool cantLoadMore() {
    return page == lastPage;
  }
}

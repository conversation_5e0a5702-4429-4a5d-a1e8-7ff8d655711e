import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../data/enums/page_loading_status.dart';
import '../data/models/family_tree_node.dart';
import '../data/models/family_tree_connection_request.dart';
import '../data/services/family_tree_connection_service.dart';

class FamilyTreeConnectionController extends GetxController {
  static FamilyTreeConnectionController get instance => Get.find();

  // Search functionality
  final TextEditingController searchController = TextEditingController();
  var searchQuery = ''.obs;
  var isSearching = false.obs;

  // Family tree nodes
  var familyTreeNodes = <FamilyTreeNode>[].obs;
  var currentPage = 1.obs;
  var totalPages = 1.obs;
  var hasNextPage = false.obs;
  var isLoadingNodes = false.obs;
  var isLoadingMore = false.obs;

  // Connection requests
  var connectionRequests = <FamilyTreeConnectionRequest>[].obs;
  var requestsCurrentPage = 1.obs;
  var requestsTotalPages = 1.obs;
  var requestsHasNextPage = false.obs;
  var isLoadingRequests = false.obs;
  var isLoadingMoreRequests = false.obs;

  // Selected node for connection request
  var selectedNode = Rxn<FamilyTreeNode>();

  // Form data
  final TextEditingController noteController = TextEditingController();
  var isSubmittingRequest = false.obs;

  // Page loading status
  var pageLoadingStatus = PageLoadingStatus.done.obs;
  var requestsPageLoadingStatus = PageLoadingStatus.done.obs;

  final FamilyTreeConnectionService _connectionService =
      FamilyTreeConnectionService.instance;

  @override
  void onInit() {
    super.onInit();
    loadFamilyTreeNodes();

    // Setup search debouncing
    debounce(searchQuery, (_) => searchNodes(),
        time: const Duration(milliseconds: 500));
  }

  @override
  void onClose() {
    searchController.dispose();
    noteController.dispose();
    super.onClose();
  }

  /// Load family tree nodes with search
  Future<void> loadFamilyTreeNodes({bool refresh = false}) async {
    if (refresh) {
      currentPage.value = 1;
      familyTreeNodes.clear();
    }

    if (isLoadingNodes.value) return;

    isLoadingNodes.value = true;
    if (currentPage.value == 1) {
      pageLoadingStatus.value = PageLoadingStatus.loading;
    }

    try {
      final response = await _connectionService.searchFamilyTreeNodes(
        search: searchQuery.value.isEmpty ? null : searchQuery.value,
        page: currentPage.value,
        perPage: 15,
      );

      if (response != null) {
        if (currentPage.value == 1) {
          familyTreeNodes.value = response.nodes;
        } else {
          familyTreeNodes.addAll(response.nodes);
        }

        totalPages.value = response.lastPage;
        hasNextPage.value = response.hasNextPage;
        pageLoadingStatus.value = PageLoadingStatus.done;
      } else {
        pageLoadingStatus.value = PageLoadingStatus.failed;
      }
    } catch (e) {
      pageLoadingStatus.value = PageLoadingStatus.failed;
    } finally {
      isLoadingNodes.value = false;
    }
  }

  /// Load more nodes for pagination
  Future<void> loadMoreNodes() async {
    if (!hasNextPage.value || isLoadingMore.value) return;

    isLoadingMore.value = true;
    currentPage.value++;

    try {
      final response = await _connectionService.searchFamilyTreeNodes(
        search: searchQuery.value.isEmpty ? null : searchQuery.value,
        page: currentPage.value,
        perPage: 15,
      );

      if (response != null) {
        familyTreeNodes.addAll(response.nodes);
        hasNextPage.value = response.hasNextPage;
      }
    } catch (e) {
      currentPage.value--; // Revert page increment on error
    } finally {
      isLoadingMore.value = false;
    }
  }

  /// Search nodes based on query
  void searchNodes() {
    currentPage.value = 1;
    loadFamilyTreeNodes(refresh: true);
  }

  /// Update search query
  void updateSearchQuery(String query) {
    searchQuery.value = query;
  }

  /// Clear search
  void clearSearch() {
    searchController.clear();
    searchQuery.value = '';
    loadFamilyTreeNodes(refresh: true);
  }

  /// Select a node for connection request
  void selectNode(FamilyTreeNode node) {
    selectedNode.value = node;
  }

  /// Submit connection request
  Future<bool> submitConnectionRequest() async {
    if (selectedNode.value == null || noteController.text.trim().isEmpty) {
      return false;
    }

    isSubmittingRequest.value = true;

    try {
      final success = await _connectionService.createConnectionRequest(
        nodeId: selectedNode.value!.id,
        note: noteController.text.trim(),
      );

      if (success) {
        noteController.clear();
        selectedNode.value = null;
        // Refresh connection requests list
        // loadConnectionRequests(refresh: true);
      }

      return success;
    } finally {
      isSubmittingRequest.value = false;
    }
  }

  /// Load user's connection requests
  Future<void> loadConnectionRequests(
      {bool refresh = false, String? status}) async {
    if (refresh) {
      requestsCurrentPage.value = 1;
      connectionRequests.clear();
    }

    if (isLoadingRequests.value) return;

    isLoadingRequests.value = true;
    if (requestsCurrentPage.value == 1) {
      requestsPageLoadingStatus.value = PageLoadingStatus.loading;
    }

    try {
      final response = await _connectionService.getMyConnectionRequests(
        page: requestsCurrentPage.value,
        perPage: 15,
        status: status,
      );

      if (response != null) {
        if (requestsCurrentPage.value == 1) {
          connectionRequests.value = response.requests;
        } else {
          connectionRequests.addAll(response.requests);
        }

        requestsTotalPages.value = response.lastPage;
        requestsHasNextPage.value = response.hasNextPage;
        requestsPageLoadingStatus.value = PageLoadingStatus.done;
      } else {
        requestsPageLoadingStatus.value = PageLoadingStatus.failed;
      }
    } catch (e) {
      requestsPageLoadingStatus.value = PageLoadingStatus.failed;
    } finally {
      isLoadingRequests.value = false;
    }
  }

  /// Load more connection requests
  Future<void> loadMoreRequests({String? status}) async {
    if (!requestsHasNextPage.value || isLoadingMoreRequests.value) return;

    isLoadingMoreRequests.value = true;
    requestsCurrentPage.value++;

    try {
      final response = await _connectionService.getMyConnectionRequests(
        page: requestsCurrentPage.value,
        perPage: 15,
        status: status,
      );

      if (response != null) {
        connectionRequests.addAll(response.requests);
        requestsHasNextPage.value = response.hasNextPage;
      }
    } catch (e) {
      requestsCurrentPage.value--; // Revert page increment on error
    } finally {
      isLoadingMoreRequests.value = false;
    }
  }

  /// Refresh all data
  void refreshAll() {
    loadFamilyTreeNodes(refresh: true);
    loadConnectionRequests(refresh: true);
  }
}

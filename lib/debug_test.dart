import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'src/core/services/debug_service.dart';

/// Debug Test Widget to verify console output functionality
/// This widget tests various debug output methods to ensure they appear in the console
class DebugTestWidget extends StatelessWidget {
  const DebugTestWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Debug Console Test'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Debug Console Test',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            const Text(
              'Click the buttons below to test different debug output methods. Check your debug console for output.',
              style: TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 30),
            ElevatedButton(
              onPressed: _testPrintStatements,
              child: const Text('Test print() Statements'),
            ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: _testDebugPrint,
              child: const Text('Test debugPrint()'),
            ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: _testDeveloperLog,
              child: const Text('Test developer.log()'),
            ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: _testKDebugMode,
              child: const Text('Test kDebugMode Conditional'),
            ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: _testFlutterError,
              child: const Text('Test FlutterError.reportError()'),
            ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: _testImageCropperDebug,
              child: const Text('Test Image Cropper Debug'),
            ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: _testDebugService,
              child: const Text('Test Enhanced Debug Service'),
            ),
            const SizedBox(height: 30),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: const Text(
                'Expected Output:\n'
                '• All test messages should appear in your debug console\n'
                '• If messages don\'t appear, check your IDE debug settings\n'
                '• For VS Code: Check Debug Console tab\n'
                '• For Android Studio: Check Run/Debug window',
                style: TextStyle(fontSize: 14),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _testPrintStatements() {
    print('🔍 DEBUG TEST: Basic print() statement working!');
    print('🔍 DEBUG TEST: Current timestamp: ${DateTime.now()}');
    print('🔍 DEBUG TEST: This is a multi-line\nprint statement\nwith line breaks');
  }

  void _testDebugPrint() {
    debugPrint('🐛 DEBUG TEST: debugPrint() statement working!');
    debugPrint('🐛 DEBUG TEST: debugPrint is Flutter\'s recommended debug output method');
  }

  void _testDeveloperLog() {
    developer.log('📝 DEBUG TEST: developer.log() working!', name: 'DebugTest');
    developer.log(
      '📝 DEBUG TEST: Structured log with details',
      name: 'DebugTest',
      time: DateTime.now(),
      level: 800, // INFO level
    );
  }

  void _testKDebugMode() {
    if (kDebugMode) {
      print('🔧 DEBUG TEST: kDebugMode conditional print working!');
      print('🔧 DEBUG TEST: This only appears in debug builds');
    }
  }

  void _testFlutterError() {
    try {
      throw Exception('Test exception for debug console');
    } catch (error, stackTrace) {
      FlutterError.reportError(FlutterErrorDetails(
        exception: error,
        stack: stackTrace,
        library: 'debug_test',
        context: ErrorDescription('Testing error reporting in debug console'),
      ));
      print('⚠️ DEBUG TEST: FlutterError.reportError() called - check for error details above');
    }
  }

  void _testImageCropperDebug() {
    print('📸 DEBUG TEST: Image Cropper Debug Simulation');
    print('📸 DEBUG TEST: Simulating image cropping workflow...');

    // Simulate the image cropping workflow debug messages
    _simulateImageCroppingWorkflow();
  }

  void _testDebugService() {
    print('🧪 DEBUG TEST: Testing Enhanced Debug Service');
    DebugService.testAllMethods();
  }

  void _simulateImageCroppingWorkflow() {
    print('📸 ImageCropper: Starting image selection...');

    Future.delayed(const Duration(milliseconds: 500), () {
      print('📸 ImageCropper: Image selected successfully');
      print('📸 ImageCropper: Image path: /mock/path/to/image.jpg');

      Future.delayed(const Duration(milliseconds: 500), () {
        print('📸 ImageCropper: Starting crop operation...');
        print('📸 ImageCropper: Aspect ratio: 1:1 (square)');

        Future.delayed(const Duration(milliseconds: 1000), () {
          print('📸 ImageCropper: Crop operation completed');
          print('📸 ImageCropper: Cropped image saved to: /mock/path/to/cropped_image.jpg');
          print('📸 ImageCropper: Upload starting...');

          Future.delayed(const Duration(milliseconds: 800), () {
            print('📸 ImageCropper: Upload completed successfully');
            print('📸 ImageCropper: Profile image updated');
          });
        });
      });
    });
  }
}

# Flutter Web Deployment Scripts for Almashal Family App

This directory contains automated deployment scripts for building and deploying the Almashal Family Flutter web application.

## 📁 Scripts Overview

### 1. `deploy-web.sh` - Full Build & Deploy
**Complete automation script that builds and deploys the web app**

- ✅ Builds Flutter web app for production
- ✅ Cleans target directory
- ✅ Copies files to Laravel public directory
- ✅ Verifies deployment
- ✅ Provides detailed progress output

### 2. `build-web.sh` - Build Only
**Builds the web app without deploying**

- ✅ Builds Flutter web app for production
- ✅ Verifies build integrity
- ✅ Useful for testing builds locally

## 🚀 Quick Start

### Make Scripts Executable (First Time Only)
```bash
chmod +x deploy-web.sh
chmod +x build-web.sh
```

### Deploy to Production
```bash
./deploy-web.sh
```

### Build Only (No Deployment)
```bash
./build-web.sh
```

## 📋 What the Scripts Do

### Pre-flight Checks
- ✅ Verifies Flutter installation
- ✅ Confirms you're in a Flutter project directory
- ✅ Checks Flutter version and web support

### Build Process
- 🧹 Cleans previous build artifacts (`flutter clean`)
- 📦 Gets latest dependencies (`flutter pub get`)
- 🏗️ Builds for web with release mode and correct base-href
- ✅ Verifies build completion and integrity

### Deployment Process (deploy-web.sh only)
- 📁 Creates target directory if needed
- 🧹 Cleans existing files in target directory
- 📋 Copies all build files to Laravel public directory
- ✅ Verifies deployment success
- 📊 Shows deployment summary

## 🎯 Target Configuration

### Build Configuration
- **Mode**: Release (`--release`)
- **Base HREF**: `/app/` (`--base-href="/app/"`)
- **Output**: `build/web/`

### Deployment Target
- **Directory**: `/Users/<USER>/Desktop/laravel projects/Maalem/almashal_family/public/app/`
- **Structure**: Preserves all files and directories from `build/web/`

## 🔧 Customization

### Changing Target Directory
Edit the `TARGET_DIR` variable in `deploy-web.sh`:
```bash
TARGET_DIR="/your/custom/path/here"
```

### Changing Base HREF
Edit the `BASE_HREF` variable in both scripts:
```bash
BASE_HREF="/your-custom-path/"
```

## 📊 Script Output

The scripts provide colored, timestamped output showing:
- 🔵 **Blue**: Step information
- 🟢 **Green**: Success messages
- 🔴 **Red**: Error messages
- 🟡 **Yellow**: Warning messages
- 🟣 **Purple**: Information messages

### Example Output
```
[14:30:15] 🚀 Starting deployment process...
[14:30:16] ✅ Pre-flight checks completed
[14:30:17] 🏗️ Building Flutter web application...
[14:30:45] ✅ Flutter web build completed successfully
[14:30:46] 📁 Preparing target directory...
[14:30:47] ✅ Files deployed successfully
[14:30:48] ✅ 🚀 Deployment completed successfully!
```

## 🛠️ Troubleshooting

### Common Issues

#### "Flutter is not installed or not in PATH"
- Ensure Flutter is properly installed
- Add Flutter to your system PATH
- Restart terminal after PATH changes

#### "Not in a Flutter project directory"
- Run the script from the root of your Flutter project
- Ensure `pubspec.yaml` exists in the current directory

#### "Failed to copy files to target directory"
- Check directory permissions
- Ensure the target path exists and is writable
- Verify you have sufficient disk space

#### "Build appears incomplete"
- Check for Flutter build errors in the output
- Ensure all dependencies are properly installed
- Try running `flutter clean` manually first

### Manual Verification

After deployment, verify these files exist in the target directory:
- ✅ `index.html`
- ✅ `main.dart.js`
- ✅ `flutter.js`
- ✅ `manifest.json`
- ✅ `assets/` directory

## 🧪 Testing Deployment

### Local Testing
After building, you can test locally:
```bash
cd build/web
python -m http.server 8080
# Visit http://localhost:8080 in your browser
```

### Production Testing
After deployment, visit your Laravel app URL:
```
https://your-domain.com/app/
```

## 📝 Maintenance

### Regular Tasks
1. **Update scripts** when changing deployment paths
2. **Test scripts** after Flutter updates
3. **Monitor build times** and optimize if needed
4. **Check disk space** in target directory regularly

### Script Updates
When modifying scripts:
1. Test changes in a development environment first
2. Keep backups of working versions
3. Update this README if configuration changes

## 🔒 Security Notes

- Scripts include error handling to prevent partial deployments
- Target directory is cleaned before each deployment
- Build verification ensures integrity before deployment
- No sensitive information is logged or exposed

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Review script output for specific error messages
3. Ensure all prerequisites are met
4. Test with a fresh Flutter project if needed

---

**Last Updated**: $(date)
**Flutter Version**: Run `flutter --version` to check your version
**Target Environment**: Laravel Public Directory

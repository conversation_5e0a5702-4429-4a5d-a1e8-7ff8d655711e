{
  "cSpell.words": [
    "إدخال"
  ],
  // Flutter Debug Configuration with VM Service Fix
  "dart.debugExternalPackageLibraries": true,
  "dart.debugSdkLibraries": true,
  "dart.showInspectorNotificationsForWidgetErrors": true,
  "dart.previewFlutterUiGuides": true,
  "dart.previewFlutterUiGuidesCustomTracking": true,
  "dart.flutterHotReloadOnSave": "always",
  "dart.flutterHotRestartOnSave": "never",
  "dart.openDevTools": "flutter",
  "dart.devToolsTheme": "dark",
  "dart.showMainCodeLens": true,
  "dart.showTestCodeLens": true,
  "dart.analysisServerFolding": true,
  "dart.closingLabels": true,
  "dart.enableCompletionCommitCharacters": true,
  "dart.enableSnippets": true,
  "dart.insertArgumentPlaceholders": true,
  "dart.runPubGetOnPubspecChanges": "prompt",
  "dart.warnWhenEditingFilesOutsideWorkspace": true,
  "dart.allowAnalytics": false,
  "dart.checkForSdkUpdates": true,
  "dart.flutterCreateAndroidLanguage": "kotlin",
  "dart.flutterCreateIOSLanguage": "swift",
  "dart.flutterCreateOrganization": "com.almashalfamily",
  "dart.maxLogLineLength": 2000,
  "dart.showSkippedTests": true,
  // VM Service Configuration (Fix for timeout issues)
  "dart.vmServicePort": 8888,
  "dart.vmServiceLogFile": "dart_vm_service.log",
  "dart.flutterRunLogFile": "flutter_run.log",
  "dart.analyzerLogFile": "analyzer.log",
  "dart.extensionLogFile": "dart_extension.log",
  // Console and Debug Output Settings
  "debug.console.fontSize": 14,
  "debug.console.fontFamily": "Monaco, 'Courier New', monospace",
  "debug.console.wordWrap": true,
  "debug.console.acceptSuggestionOnEnter": "off",
  "debug.console.collapseIdenticalLines": false,
  "debug.console.historySuggestions": true,
  "debug.internalConsoleOptions": "openOnSessionStart",
  "debug.openDebug": "openOnSessionStart",
  "debug.showBreakpointsInOverviewRuler": true,
  "debug.showInlineBreakpointCandidates": true,
  // Terminal Settings for Flutter
  "terminal.integrated.fontSize": 14,
  "terminal.integrated.fontFamily": "Monaco, 'Courier New', monospace",
  "terminal.integrated.lineHeight": 1.2,
  "terminal.integrated.scrollback": 10000,
  "terminal.integrated.fastScrollSensitivity": 5,
  "terminal.integrated.smoothScrolling": true,
  "terminal.integrated.enableVisualBell": false,
  // Editor Settings for Better Debugging
  "editor.rulers": [
    80,
    120
  ],
  "editor.wordWrap": "bounded",
  "editor.wordWrapColumn": 120,
  "editor.minimap.enabled": true,
  "editor.minimap.showSlider": "always",
  "editor.bracketPairColorization.enabled": true,
  "editor.guides.bracketPairs": true,
  "editor.guides.bracketPairsHorizontal": true,
  "editor.guides.highlightActiveIndentation": true,
  "editor.guides.indentation": true,
  // File Association
  "files.associations": {
    "*.dart": "dart",
    "pubspec.yaml": "yaml",
    "analysis_options.yaml": "yaml"
  },
  // Search and File Exclusions
  "search.exclude": {
    "**/build/**": true,
    "**/.dart_tool/**": true,
    "**/ios/Pods/**": true,
    "**/android/.gradle/**": true,
    "**/android/app/build/**": true
  },
  // File Watcher Exclusions
  "files.watcherExclude": {
    "**/build/**": true,
    "**/.dart_tool/**": true,
    "**/ios/Pods/**": true,
    "**/android/.gradle/**": true,
    "**/android/app/build/**": true
  },
  // Accessibility Settings
  "accessibility.signals.terminalBell": {
    "sound": "off"
  }
}
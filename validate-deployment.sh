#!/bin/bash

# =============================================================================
# Flutter Web Deployment Validation Script for Almashal Family App
# =============================================================================
# This script validates that the web deployment is working correctly
# by checking files, permissions, and basic functionality.
#
# Usage: ./validate-deployment.sh
# Make executable: chmod +x validate-deployment.sh
# =============================================================================

# Script configuration
SCRIPT_NAME="Almashal Family Web Deployment Validator"
TARGET_DIR="/Users/<USER>/Desktop/laravel projects/Maalem/almashal_family/public/app"

# Colors for console output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# =============================================================================
# Utility Functions
# =============================================================================

print_step() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} ${CYAN}$1${NC}"
}

print_success() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} ${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} ${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} ${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} ${PURPLE}ℹ️  $1${NC}"
}

print_header() {
    echo -e "${CYAN}=============================================================================${NC}"
    echo -e "${CYAN}                    $SCRIPT_NAME${NC}"
    echo -e "${CYAN}=============================================================================${NC}"
    echo ""
}

# =============================================================================
# Validation Functions
# =============================================================================

check_directory_exists() {
    print_step "Checking if deployment directory exists..."
    
    if [ ! -d "$TARGET_DIR" ]; then
        print_error "Deployment directory does not exist: $TARGET_DIR"
        return 1
    fi
    
    print_success "Deployment directory exists"
    print_info "Directory: $TARGET_DIR"
    return 0
}

check_essential_files() {
    print_step "Checking essential Flutter web files..."
    
    local essential_files=(
        "index.html"
        "main.dart.js"
        "flutter.js"
        "flutter_bootstrap.js"
        "manifest.json"
        "favicon.png"
    )
    
    local missing_files=()
    local found_files=()
    
    for file in "${essential_files[@]}"; do
        if [ -f "$TARGET_DIR/$file" ]; then
            found_files+=("$file")
            print_success "✓ $file"
        else
            missing_files+=("$file")
            print_error "✗ $file (missing)"
        fi
    done
    
    if [ ${#missing_files[@]} -eq 0 ]; then
        print_success "All essential files are present"
        return 0
    else
        print_warning "${#missing_files[@]} essential files are missing"
        return 1
    fi
}

check_assets_directory() {
    print_step "Checking assets directory..."
    
    if [ ! -d "$TARGET_DIR/assets" ]; then
        print_error "Assets directory is missing"
        return 1
    fi
    
    print_success "Assets directory exists"
    
    # Check for common asset files
    local asset_files=(
        "AssetManifest.json"
        "FontManifest.json"
        "NOTICES"
    )
    
    for file in "${asset_files[@]}"; do
        if [ -f "$TARGET_DIR/assets/$file" ]; then
            print_success "✓ assets/$file"
        else
            print_warning "✗ assets/$file (missing)"
        fi
    done
    
    return 0
}

check_file_permissions() {
    print_step "Checking file permissions..."
    
    # Check if files are readable
    if [ ! -r "$TARGET_DIR/index.html" ]; then
        print_error "index.html is not readable"
        return 1
    fi
    
    # Check directory permissions
    if [ ! -x "$TARGET_DIR" ]; then
        print_error "Deployment directory is not executable"
        return 1
    fi
    
    print_success "File permissions are correct"
    return 0
}

check_index_html_content() {
    print_step "Validating index.html content..."
    
    local index_file="$TARGET_DIR/index.html"
    
    if [ ! -f "$index_file" ]; then
        print_error "index.html not found"
        return 1
    fi
    
    # Check for base href
    if grep -q 'base href="/app/"' "$index_file"; then
        print_success "✓ Base HREF is correctly set to /app/"
    else
        print_warning "✗ Base HREF may not be correctly set"
    fi
    
    # Check for Flutter bootstrap
    if grep -q "flutter_bootstrap.js" "$index_file"; then
        print_success "✓ Flutter bootstrap script is referenced"
    else
        print_warning "✗ Flutter bootstrap script reference missing"
    fi
    
    # Check for app title
    if grep -q "أسرة المشعل" "$index_file"; then
        print_success "✓ App title is present"
    else
        print_warning "✗ App title may be missing"
    fi
    
    return 0
}

check_deployment_size() {
    print_step "Checking deployment size..."
    
    if [ ! -d "$TARGET_DIR" ]; then
        print_error "Cannot check size - directory missing"
        return 1
    fi
    
    local size=$(du -sh "$TARGET_DIR" 2>/dev/null | cut -f1)
    
    if [ -n "$size" ]; then
        print_success "Deployment size: $size"
        
        # Basic size validation (should be at least a few MB for a Flutter web app)
        local size_bytes=$(du -s "$TARGET_DIR" 2>/dev/null | cut -f1)
        if [ "$size_bytes" -gt 1000 ]; then  # More than 1MB
            print_success "Deployment size appears reasonable"
        else
            print_warning "Deployment size seems unusually small"
        fi
    else
        print_warning "Could not determine deployment size"
    fi
    
    return 0
}

generate_validation_report() {
    print_step "Generating validation report..."
    
    echo ""
    echo -e "${CYAN}=============================================================================${NC}"
    echo -e "${CYAN}                        VALIDATION REPORT${NC}"
    echo -e "${CYAN}=============================================================================${NC}"
    
    print_info "Deployment Directory: $TARGET_DIR"
    print_info "Validation Time: $(date)"
    
    # Count files
    local file_count=$(find "$TARGET_DIR" -type f 2>/dev/null | wc -l)
    print_info "Total Files: $file_count"
    
    # List main directories
    print_info "Directory Structure:"
    if [ -d "$TARGET_DIR" ]; then
        ls -la "$TARGET_DIR" | grep "^d" | awk '{print "  - " $9}' | grep -v "^\.$\|^\.\.$"
    fi
    
    echo -e "${CYAN}=============================================================================${NC}"
}

# =============================================================================
# Main Execution
# =============================================================================

main() {
    print_header
    print_step "Starting deployment validation..."
    
    local validation_passed=true
    
    # Run all validation checks
    check_directory_exists || validation_passed=false
    check_essential_files || validation_passed=false
    check_assets_directory || validation_passed=false
    check_file_permissions || validation_passed=false
    check_index_html_content || validation_passed=false
    check_deployment_size || validation_passed=false
    
    # Generate report
    generate_validation_report
    
    # Final result
    echo ""
    if [ "$validation_passed" = true ]; then
        print_success "🎉 Deployment validation PASSED!"
        print_info "Your Flutter web app appears to be correctly deployed."
        echo ""
        print_info "Next steps:"
        print_info "1. Test the application in a web browser"
        print_info "2. Check browser console for any runtime errors"
        print_info "3. Verify all app features work as expected"
    else
        print_error "❌ Deployment validation FAILED!"
        print_info "Please review the errors above and re-deploy if necessary."
        echo ""
        print_info "To re-deploy, run: ./deploy-web.sh"
    fi
    
    echo ""
    exit 0
}

# Execute main function
main "$@"

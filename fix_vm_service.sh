#!/bin/bash

echo "🔧 Flutter VM Service Timeout Fix Script"
echo "========================================"

# Kill existing Flutter/Dart processes
echo "1. Killing existing Flutter/Dart processes..."
pkill -f flutter 2>/dev/null || true
pkill -f dart 2>/dev/null || true
sleep 2

# Clean Flutter project
echo "2. Cleaning Flutter project..."
flutter clean

# Clear caches
echo "3. Clearing Flutter caches..."
flutter pub cache clean
dart pub cache clean

# Remove problematic files
echo "4. Removing temporary files..."
rm -rf .dart_tool/ 2>/dev/null || true
rm -rf build/ 2>/dev/null || true

# Reinstall dependencies
echo "5. Reinstalling dependencies..."
flutter pub get

# Check for port conflicts
echo "6. Checking for port conflicts..."
lsof -i :8888 2>/dev/null && echo "⚠️  Port 8888 is in use" || echo "✅ Port 8888 is available"
lsof -i :8889 2>/dev/null && echo "⚠️  Port 8889 is in use" || echo "✅ Port 8889 is available"

echo ""
echo "🎯 Fix completed! Now try one of these commands:"
echo ""
echo "For Chrome (Web):"
echo "flutter run --debug -d chrome --vm-service-port=8888 --web-port=3000"
echo ""
echo "For iOS Simulator:"
echo "flutter run --debug -d 'iPhone 15' --vm-service-port=8889"
echo ""
echo "For macOS Desktop:"
echo "flutter run --debug -d macos --vm-service-port=8890"
echo ""
echo "Or use VS Code debug configurations (F5) with the updated launch.json"
echo ""
echo "📋 If issues persist, check the logs:"
echo "- flutter_run.log"
echo "- dart_vm_service.log"
echo "- analyzer.log"
echo ""

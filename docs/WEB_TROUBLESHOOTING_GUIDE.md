# Flutter Web Troubleshooting Guide

## Common Issues and Solutions

### 1. Build Errors

#### Issue: "Target of URI doesn't exist" for web-specific imports
**Solution**: Wrap web-specific imports in conditional imports:
```dart
import 'dart:ui_web' as ui_web;
import 'package:universal_html/html.dart' as html;
```

#### Issue: Package compatibility errors
**Solution**: Check package documentation for web support and use platform-specific implementations:
```dart
if (PlatformHelper.isWeb) {
  // Use web-compatible alternative
} else {
  // Use mobile package
}
```

### 2. Runtime Errors

#### Issue: Firebase initialization fails on web
**Solution**: 
1. Verify Firebase web configuration in `lib/firebase_options.dart`
2. Ensure Firebase SDK scripts are loaded in `web/index.html`
3. Check browser console for specific Firebase errors

#### Issue: Image cropping not working on web
**Solution**:
1. Verify cropperjs CDN is loaded in `web/index.html`
2. Check network tab for CDN loading errors
3. Ensure `image_cropper` package supports web platform

#### Issue: PDF viewer shows blank screen on web
**Solution**:
1. Check if PDF URL is accessible from browser
2. Verify CORS headers allow iframe embedding
3. Use fallback link to open PDF in new tab

### 3. Performance Issues

#### Issue: Slow initial loading on web
**Solutions**:
- Enable web renderer optimization: `flutter build web --web-renderer canvaskit`
- Use tree-shaking: `flutter build web --tree-shake-icons`
- Optimize images and assets
- Enable gzip compression on web server

#### Issue: Large bundle size
**Solutions**:
- Analyze bundle: `flutter build web --analyze-size`
- Remove unused dependencies
- Use deferred loading for large features
- Optimize font loading

### 4. UI/UX Issues

#### Issue: Responsive design problems
**Solutions**:
- Test on different screen sizes
- Use `MediaQuery` for responsive layouts
- Implement proper breakpoints
- Test on actual devices, not just browser dev tools

#### Issue: Arabic text rendering issues
**Solutions**:
- Verify RTL configuration in `web/manifest.json`
- Check font loading for Arabic fonts
- Test text direction in different browsers

### 5. Network and API Issues

#### Issue: CORS errors when calling APIs
**Solutions**:
- Configure CORS on backend server
- Use proxy during development
- Ensure API endpoints support web requests

#### Issue: File upload/download not working
**Solutions**:
- Check browser file API support
- Verify file size limits
- Test with different file types
- Check network tab for upload errors

### 6. Browser-Specific Issues

#### Issue: Features not working in Safari
**Solutions**:
- Check Safari web API support
- Test with Safari Technology Preview
- Implement Safari-specific workarounds
- Verify HTTPS requirements

#### Issue: Firefox compatibility problems
**Solutions**:
- Test with latest Firefox version
- Check Firefox developer tools console
- Verify web standards compliance
- Test with Firefox ESR

### 7. Development Environment Issues

#### Issue: Hot reload not working on web
**Solutions**:
- Restart Flutter development server
- Clear browser cache
- Check for browser extensions interfering
- Use `flutter run -d chrome --web-port=8080`

#### Issue: Chrome DevTools not connecting
**Solutions**:
- Ensure Chrome is updated
- Check firewall settings
- Try different port: `--web-port=8081`
- Restart Flutter daemon: `flutter daemon --shutdown`

## Debugging Commands

### Check Flutter Web Support
```bash
flutter doctor
flutter devices
```

### Build with Verbose Output
```bash
flutter build web --verbose
```

### Run with Specific Browser
```bash
flutter run -d chrome
flutter run -d edge
flutter run -d firefox
```

### Analyze Bundle Size
```bash
flutter build web --analyze-size
```

### Check Dependencies
```bash
flutter pub deps
flutter pub outdated
```

## Browser Developer Tools

### Chrome DevTools
- Network tab: Check for failed resource loading
- Console tab: Look for JavaScript errors
- Application tab: Check service worker and storage
- Performance tab: Analyze loading performance

### Firefox Developer Tools
- Network Monitor: Check API calls and resource loading
- Web Console: Look for errors and warnings
- Storage Inspector: Check local storage and cookies

### Safari Web Inspector
- Network tab: Monitor resource loading
- Console tab: Check for errors
- Storage tab: Inspect local storage

## Performance Monitoring

### Key Metrics to Monitor
- First Contentful Paint (FCP)
- Largest Contentful Paint (LCP)
- Time to Interactive (TTI)
- Bundle size and loading time

### Tools for Monitoring
- Chrome Lighthouse
- WebPageTest
- Flutter DevTools
- Browser Performance tab

## Common Web-Specific Considerations

### Security
- HTTPS required for many web APIs
- Content Security Policy (CSP) configuration
- CORS configuration for API calls

### Accessibility
- Keyboard navigation support
- Screen reader compatibility
- Proper ARIA labels
- Color contrast compliance

### SEO (if applicable)
- Meta tags configuration
- Open Graph tags
- Structured data
- Sitemap generation

## Getting Help

### Official Resources
- [Flutter Web Documentation](https://flutter.dev/web)
- [Flutter Web FAQ](https://flutter.dev/docs/development/platform-integration/web)
- [Flutter GitHub Issues](https://github.com/flutter/flutter/issues)

### Community Resources
- Flutter Discord
- Stack Overflow (flutter-web tag)
- Reddit r/FlutterDev
- Flutter Community Slack

### Debugging Best Practices
1. Always check browser console first
2. Test in multiple browsers
3. Use Flutter DevTools for debugging
4. Check network tab for API issues
5. Verify platform-specific code paths
6. Test with different screen sizes
7. Monitor performance metrics

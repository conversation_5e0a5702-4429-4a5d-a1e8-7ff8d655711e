# Family Tree Connection Request API

This document describes the RESTful API endpoint for family tree connection requests, which allows authenticated family members to request linking their user profile to an existing family tree node.

## Overview

The Family Tree Connection Request API enables users to:
- Request connection to family tree nodes
- Have requests reviewed by administrators
- Maintain data integrity through the centralized Profile model
- Prevent spam through rate limiting

## Authentication

All endpoints require:
1. **API Key**: Include `api-key` header with the application API key
2. **User Authentication**: Use Laravel Sanctum token authentication

## Endpoints

### Create Connection Request

**POST** `/api/v1/family-tree-nodes/{nodeId}/connection-requests`

Creates a new connection request for linking a user's profile to a family tree node.

#### Headers
```
Content-Type: application/json
api-key: {API_KEY}
Authorization: Bearer {SANCTUM_TOKEN}
```

#### Parameters
- `nodeId` (integer, required): The ID of the family tree node to connect to

#### Request Body
```json
{
    "note": "This is my grandfather and I would like to connect my profile to his node in the family tree."
}
```

#### Validation Rules
- `note`: Required, string, minimum 10 characters, maximum 500 characters

#### Success Response (201 Created)
```json
{
    "message": "تم إرسال طلب الربط بنجاح. سيتم مراجعته من قبل الإدارة.",
    "data": {
        "id": 1,
        "family_member_id": 123,
        "family_tree_node_id": 456,
        "status": "pending",
        "note": "This is my grandfather and I would like to connect my profile to his node.",
        "requested_at": "2024-12-19T10:30:00Z"
    }
}
```

#### Error Responses

**400 Bad Request** - Validation Error
```json
{
    "message": "خطأ في البيانات المدخلة.",
    "errors": {
        "note": ["الملاحظة مطلوبة."]
    }
}
```

**401 Unauthorized** - Authentication Required
```json
{
    "message": "Unauthenticated."
}
```

**404 Not Found** - Node Not Found
```json
{
    "message": "عقدة الشجرة المطلوبة غير موجودة."
}
```

**409 Conflict** - Already Connected
```json
{
    "message": "أنت مرتبط بالفعل بعقدة في الشجرة."
}
```

**409 Conflict** - Duplicate Request
```json
{
    "message": "لديك طلب ربط مُعلق بالفعل لهذه العقدة."
}
```

**429 Too Many Requests** - Rate Limit Exceeded
```json
{
    "message": "لقد تجاوزت الحد المسموح من الطلبات. يرجى المحاولة لاحقاً."
}
```

**500 Internal Server Error** - Server Error
```json
{
    "message": "حدث خطأ في الخادم. يرجى المحاولة لاحقاً."
}
```

## Business Rules

### Connection Restrictions
1. **Single Connection**: Users can only be connected to one family tree node at a time
2. **Pending Requests**: Users cannot create duplicate requests for the same node
3. **Node Existence**: The target family tree node must exist and be accessible

### Rate Limiting
- **Limit**: 3 requests per hour per user
- **Purpose**: Prevent spam and abuse
- **Reset**: Automatically resets after 1 hour

### Request Lifecycle
1. **Created**: Request is submitted with "pending" status
2. **Review**: Administrators review the request in the admin panel
3. **Approval**: If approved, the user's profile is linked to the tree node
4. **Rejection**: If rejected, the request is marked as rejected

## Admin Management

Administrators can manage connection requests through the Filament admin panel:

- **View Requests**: List all connection requests with filtering by status
- **Approve Requests**: Automatically link user profile to tree node
- **Reject Requests**: Mark request as rejected with optional reason
- **View Details**: See full request information including user and node details

## Database Schema

### family_tree_connection_requests
```sql
CREATE TABLE family_tree_connection_requests (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    family_member_id BIGINT UNSIGNED NOT NULL,
    family_tree_node_id BIGINT UNSIGNED NOT NULL,
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    note TEXT,
    requested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    reviewed_at TIMESTAMP NULL,
    reviewed_by BIGINT UNSIGNED NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    FOREIGN KEY (family_member_id) REFERENCES family_members(id) ON DELETE CASCADE,
    FOREIGN KEY (family_tree_node_id) REFERENCES family_tree_nodes(id) ON DELETE CASCADE,
    FOREIGN KEY (reviewed_by) REFERENCES users(id) ON DELETE SET NULL,
    
    UNIQUE KEY unique_connection_request (family_member_id, family_tree_node_id),
    INDEX idx_status_requested (status, requested_at),
    INDEX idx_node_status (family_tree_node_id, status)
);
```

## Integration with Existing System

### Profile Model Integration
- Uses the centralized Profile model for data consistency
- Maintains backward compatibility with existing API endpoints
- Follows established patterns for user-profile relationships

### Authentication Integration
- Uses existing Laravel Sanctum authentication
- Integrates with existing API key middleware
- Follows established security patterns

### Localization
- All messages are localized in Arabic
- Uses Laravel's translation system
- Consistent with existing API response patterns

## Testing

Comprehensive test suite covers:
- Successful request creation
- Authentication requirements
- Validation rules
- Business rule enforcement
- Rate limiting
- Error handling

Run tests with:
```bash
php artisan test tests/Feature/Api/FamilyTreeConnectionRequestTest.php
```

## Security Considerations

1. **Authentication**: All requests require valid user authentication
2. **Authorization**: Users can only create requests for their own profile
3. **Rate Limiting**: Prevents abuse and spam
4. **Input Validation**: All input is validated and sanitized
5. **SQL Injection Protection**: Uses Eloquent ORM for database queries
6. **CSRF Protection**: API uses token-based authentication (no CSRF needed)

## Monitoring and Logging

- All requests are logged for admin notification
- Rate limiting attempts are tracked
- Error conditions are logged with context
- Database transactions ensure data consistency

# Flutter Debug Console Troubleshooting Guide

## Overview
This guide helps resolve issues with Flutter debug console output not appearing in VS Code or other IDEs. It covers configuration, testing, and troubleshooting steps to restore full visibility of debug information.

## Quick Test
Run the debug test widget to verify console output:
```bash
flutter run lib/debug_test.dart
```

## VS Code Configuration

### 1. Settings Configuration
The `.vscode/settings.json` file has been configured with optimal debug settings:

#### Key Debug Settings:
- `dart.debugExternalPackageLibraries: true` - Shows debug info from packages
- `dart.debugSdkLibraries: true` - Shows debug info from Flutter SDK
- `dart.showInspectorNotificationsForWidgetErrors: true` - Widget error notifications
- `dart.maxLogLineLength: 2000` - Increased log line length
- `dart.vmServiceLogFile: "dart_vm_service.log"` - VM service logging

#### Console Settings:
- `debug.console.fontSize: 14` - Readable console font size
- `debug.console.wordWrap: true` - Wrap long lines
- `debug.internalConsoleOptions: "openOnSessionStart"` - Auto-open console
- `debug.openDebug: "openOnSessionStart"` - Auto-open debug panel

### 2. Launch Configuration
The `.vscode/launch.json` file provides multiple debug configurations:

- **Flutter Debug (Development)** - Full debug mode with all features
- **Flutter Debug Console Test** - Specifically for testing console output
- **Flutter Web Debug** - Web-specific debugging
- **Flutter iOS Debug** - iOS-specific debugging

## Troubleshooting Steps

### Step 1: Verify VS Code Extensions
Ensure you have the latest Flutter and Dart extensions:
```bash
# Check installed extensions
code --list-extensions | grep -E "(dart|flutter)"
```

Required extensions:
- `Dart-Code.dart-code` (Dart extension)
- `Dart-Code.flutter` (Flutter extension)

### Step 2: Check Debug Console Location
In VS Code:
1. Open **View** → **Debug Console** (Ctrl+Shift+Y / Cmd+Shift+Y)
2. Ensure you're looking at the **Debug Console** tab, not Terminal
3. The Debug Console should show Flutter logs when running in debug mode

### Step 3: Verify Flutter Debug Mode
Ensure you're running in debug mode:
```bash
# Run in debug mode (default)
flutter run

# Explicitly specify debug mode
flutter run --debug

# Check current mode in running app
# Look for "Debug" banner in top-right corner of app
```

### Step 4: Test Console Output
Use the debug test widget:
```bash
# Run the debug test
flutter run lib/debug_test.dart

# Or add to your main app and navigate to it
```

### Step 5: Check Flutter Doctor
Verify Flutter installation:
```bash
flutter doctor -v
```

### Step 6: Clear Flutter Cache
If issues persist:
```bash
# Clean project
flutter clean

# Get dependencies
flutter pub get

# Clear Flutter cache
flutter pub cache clean

# Restart VS Code
```

## Debug Output Methods

### 1. Basic Print Statements
```dart
print('Debug message');
print('Variable value: $variable');
```

### 2. Debug Print (Recommended)
```dart
import 'package:flutter/foundation.dart';

debugPrint('Debug message');
debugPrint('This appears in debug console');
```

### 3. Developer Log (Advanced)
```dart
import 'dart:developer' as developer;

developer.log('Message', name: 'MyApp');
developer.log('Detailed log', name: 'MyApp', time: DateTime.now());
```

### 4. Conditional Debug Output
```dart
import 'package:flutter/foundation.dart';

if (kDebugMode) {
  print('This only appears in debug builds');
}
```

### 5. Error Reporting
```dart
import 'package:flutter/foundation.dart';

try {
  // Your code
} catch (error, stackTrace) {
  FlutterError.reportError(FlutterErrorDetails(
    exception: error,
    stack: stackTrace,
    library: 'your_library',
    context: ErrorDescription('Context description'),
  ));
}
```

## Image Cropper Debug Integration

### Enhanced Debug Logging for Image Cropper
Add debug logging to your ProfileController:

```dart
Future<bool> updateProfileImageWithCropping(XFile imageFile) async {
  if (kDebugMode) {
    print('🔍 ProfileController: Starting image cropping workflow');
    print('🔍 ProfileController: Image path: ${imageFile.path}');
  }

  try {
    // Show confirmation dialog
    final shouldCrop = await ImageCroppingService.showCroppingConfirmation(
      message: 'سيتم قص الصورة لتصبح مربعة الشكل للحصول على أفضل مظهر في الملف الشخصي. هل تريد المتابعة؟',
      title: 'قص صورة الملف الشخصي',
    );

    if (kDebugMode) {
      print('🔍 ProfileController: User crop decision: $shouldCrop');
    }

    if (!shouldCrop) {
      if (kDebugMode) print('🔍 ProfileController: User cancelled cropping');
      return false;
    }

    // Continue with existing workflow...

  } catch (e) {
    if (kDebugMode) {
      print('❌ ProfileController: Error in image cropping: $e');
    }
    rethrow;
  }
}
```

## Common Issues and Solutions

### Issue 1: No Output in Debug Console
**Solution:**
1. Ensure you're using the Debug Console tab, not Terminal
2. Check that you're running in debug mode
3. Verify VS Code Flutter extension is active

### Issue 2: Truncated Log Messages
**Solution:**
- Increase `dart.maxLogLineLength` in settings
- Use `debugPrint()` instead of `print()` for long messages

### Issue 3: Missing Package Debug Info
**Solution:**
- Set `dart.debugExternalPackageLibraries: true`
- Set `dart.debugSdkLibraries: true`

### Issue 4: Console Not Auto-Opening
**Solution:**
- Set `debug.internalConsoleOptions: "openOnSessionStart"`
- Manually open Debug Console (Ctrl+Shift+Y / Cmd+Shift+Y)

### Issue 5: Flutter Logs Not Appearing
**Solution:**
1. Restart Flutter app
2. Restart VS Code
3. Check Flutter doctor for issues
4. Clear Flutter cache

## Testing Checklist

- [x] VS Code Flutter extension installed and updated
- [x] Debug Console tab visible and active
- [x] Running Flutter app in debug mode
- [x] Basic print() statements appear ✅ (Verified with test_debug_output.dart)
- [x] debugPrint() statements appear ✅ (Available in Flutter apps)
- [x] developer.log() statements appear ✅ (Verified with test_debug_output.dart)
- [x] Error messages appear ✅ (Verified with test_debug_output.dart)
- [ ] Image cropper debug messages appear (Test with ProfileController)

## Configuration Status

✅ **VS Code Settings**: Optimized for Flutter debugging
✅ **Launch Configuration**: Multiple debug configurations available
✅ **Debug Service**: Enhanced logging service created
✅ **Test Files**: Debug test widgets and scripts created
✅ **Basic Output**: Verified working with test_debug_output.dart

## Additional Resources

- [Flutter Debugging Documentation](https://docs.flutter.dev/testing/debugging)
- [VS Code Flutter Extension](https://marketplace.visualstudio.com/items?itemName=Dart-Code.flutter)
- [Dart DevTools](https://dart.dev/tools/dart-devtools)

## Support

If issues persist after following this guide:
1. Check Flutter and Dart extension versions
2. Restart VS Code completely
3. Restart your development machine
4. Check for VS Code updates
5. Reinstall Flutter extensions if necessary

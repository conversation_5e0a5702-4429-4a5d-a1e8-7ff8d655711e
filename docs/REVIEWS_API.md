# Reviews API Documentation

This document describes the Reviews API endpoints for the Laravel backend that supports the Flutter application's profile reviews feature.

## Overview

The Reviews API allows family members to rate and review each other within the family application. Each review consists of a rating (1-5 stars) and an optional comment.

## Authentication

All review endpoints require:
1. **API Key**: Include `api-key` header with the value `NwaAi8q5SXQAu9P5X3bqSPGkakoI`
2. **Bearer Token**: Include `Authorization: Bearer {token}` header for authenticated endpoints

## Endpoints

### 1. Create Review

**POST** `/api/v1/reviews`

Create a new review for a family member.

**Headers:**
```
api-key: NwaAi8q5SXQAu9P5X3bqSPGkakoI
Authorization: Bearer {token}
Content-Type: application/json
```

**Request Body:**
```json
{
    "reviewed_user_id": 2,
    "rating": 5,
    "comment": "Great family member!"
}
```

**Response (201):**
```json
{
    "message": "Review created successfully",
    "data": {
        "id": 1,
        "rating": 5,
        "comment": "Great family member!",
        "reviewer": {
            "id": 1,
            "name": "<PERSON>",
            "image": "http://example.com/uploads/image.jpg",
            "thumb_image": "http://example.com/images/thumb/image.jpg"
        },
        "reviewed_user": {
            "id": 2,
            "name": "Jane Doe Smith",
            "image": "http://example.com/uploads/image2.jpg",
            "thumb_image": "http://example.com/images/thumb/image2.jpg"
        },
        "status": true,
        "created_at": "2025-04-18 12:00:00",
        "created_at_formatted": "18 April 2025 12:00 PM",
        "updated_at": "2025-04-18 12:00:00"
    }
}
```

### 2. Get User Reviews

**GET** `/api/v1/users/{id}/reviews`

Get all reviews for a specific user with pagination.

**Query Parameters:**
- `page` (optional): Page number for pagination
- `per_page` (optional): Number of reviews per page (max 50, default 10)

**Response (200):**
```json
{
    "data": [
        {
            "id": 1,
            "rating": 5,
            "comment": "Great family member!",
            "reviewer": {
                "id": 1,
                "name": "John Doe Smith",
                "image": "http://example.com/uploads/image.jpg",
                "thumb_image": "http://example.com/images/thumb/image.jpg"
            },
            "reviewed_user": {
                "id": 2,
                "name": "Jane Doe Smith",
                "image": "http://example.com/uploads/image2.jpg",
                "thumb_image": "http://example.com/images/thumb/image2.jpg"
            },
            "status": true,
            "created_at": "2025-04-18 12:00:00",
            "created_at_formatted": "18 April 2025 12:00 PM",
            "updated_at": "2025-04-18 12:00:00"
        }
    ],
    "meta": {
        "current_page": 1,
        "last_page": 1,
        "per_page": 10,
        "total": 1
    }
}
```

### 3. Get Review Summary

**GET** `/api/v1/users/{id}/reviews/summary`

Get review statistics for a specific user.

**Response (200):**
```json
{
    "data": {
        "user": {
            "id": 1,
            "name": "Jane Doe Smith",
            "image": "http://example.com/uploads/image.jpg",
            "thumb_image": "http://example.com/images/thumb/image.jpg"
        },
        "reviews_count": 5,
        "average_rating": 4.2,
        "ratings_breakdown": {
            "5": 2,
            "4": 2,
            "3": 1,
            "2": 0,
            "1": 0
        }
    }
}
```

### 4. Update Review

**PUT/PATCH** `/api/v1/reviews/{id}`

Update an existing review (only by the original reviewer).

**Request Body:**
```json
{
    "rating": 4,
    "comment": "Updated comment"
}
```

**Response (200):**
```json
{
    "message": "Review updated successfully",
    "data": {
        // Same structure as create review response
    }
}
```

### 5. Delete Review

**DELETE** `/api/v1/reviews/{id}`

Delete a review (only by the original reviewer).

**Response (200):**
```json
{
    "message": "Review deleted successfully"
}
```

## Validation Rules

### Create/Update Review
- `reviewed_user_id`: Required, must exist in family_members table, cannot be the same as reviewer
- `rating`: Required, integer between 1 and 5
- `comment`: Optional, string, maximum 1000 characters

## Business Rules

1. **No Self-Reviews**: Users cannot review themselves
2. **One Review Per User**: Each user can only review another user once
3. **Reviewer Permissions**: Only the original reviewer can update or delete their review
4. **Soft Deletes**: Deleted reviews are soft-deleted and excluded from statistics
5. **Active Reviews Only**: Only active reviews (status = true) are included in statistics and listings

## Error Responses

### Validation Error (422)
```json
{
    "message": "Validation error",
    "errors": {
        "reviewed_user_id": ["You cannot review yourself."],
        "rating": ["The rating must be between 1 and 5."]
    }
}
```

### Unauthorized (401)
```json
{
    "message": "Unauthenticated."
}
```

### Forbidden (403)
```json
{
    "message": "Unauthorized"
}
```

### Not Found (404)
```json
{
    "message": "Review not found"
}
```

## Database Schema

The reviews are stored in the `reviews` table with the following structure:

- `id`: Primary key
- `reviewer_id`: Foreign key to family_members table
- `reviewed_user_id`: Foreign key to family_members table
- `rating`: Integer (1-5)
- `comment`: Text (nullable)
- `status`: Boolean (default: true)
- `created_at`: Timestamp
- `updated_at`: Timestamp
- `deleted_at`: Timestamp (nullable, for soft deletes)

**Indexes:**
- `reviewed_user_id, status`
- `reviewer_id, status`
- `rating`

**Constraints:**
- Unique constraint on `reviewer_id, reviewed_user_id` (prevents duplicate reviews)
- Foreign key constraints with cascade delete

## Integration with FamilyMember Model

The `FamilyMember` model includes the following review-related methods:

- `reviewsGiven()`: Reviews written by this user
- `reviewsReceived()`: Reviews received by this user
- `activeReviewsReceived()`: Active reviews received by this user
- `average_rating`: Calculated average rating
- `reviews_count`: Total number of reviews
- `review_stats`: Complete statistics including breakdown by rating
